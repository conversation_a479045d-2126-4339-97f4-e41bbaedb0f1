<?php
/**
 * Conexión simplificada a la base de datos
 * Compatible con desarrollo local y hosting
 */

// Mostrar todos los errores para depuración
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Variable global para el estado de conexión
$db_connection_ok = false;
$db_error_message = '';
$db_config_used = '';

// Configuraciones de conexión por ambiente
$db_configs = [
    'local_xampp' => [
        'host' => 'localhost',
        'username' => 'root',
        'password' => '',
        'database' => 'gestarse_experian',
        'port' => 3306
    ],
    'local_tcp' => [
        'host' => '127.0.0.1',
        'username' => 'root',
        'password' => '',
        'database' => 'gestarse_experian',
        'port' => 3306
    ],
    'hosting' => [
        'host' => '*************',
        'username' => 'gestarse_ncornejo7_experian',
        'password' => 'N1c0l7as17',
        'database' => 'gestarse_experian',
        'port' => 3306
    ]
];

// Función simple de logging
function log_simple($mensaje) {
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[$timestamp] $mensaje" . PHP_EOL;
    
    // Mostrar en pantalla durante depuración
    echo "<!-- DB LOG: $log_message -->\n";
    
    // También guardar en archivo
    @file_put_contents(
        __DIR__ . '/logs/db_simple.log', 
        $log_message, 
        FILE_APPEND | LOCK_EX
    );
}

// Detectar ambiente actual
function detectar_ambiente() {
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    
    if (strpos($host, 'localhost') !== false || 
        strpos($host, '127.0.0.1') !== false ||
        strpos($host, '::1') !== false) {
        return 'local';
    }
    
    return 'hosting';
}

// Variables globales de conexión
$mysqli = null;
$conn = null;
$conexion = null;

// Detectar ambiente
$ambiente = detectar_ambiente();
log_simple("Ambiente detectado: $ambiente");

// Probar conexiones
foreach ($db_configs as $config_name => $config) {
    // Saltar configuraciones no relevantes al ambiente
    if ($ambiente === 'local' && $config_name === 'hosting') continue;
    if ($ambiente === 'hosting' && strpos($config_name, 'local') !== false) continue;
    
    log_simple("Intentando conexión con: $config_name");
    log_simple("  Host: {$config['host']}:{$config['port']}");
    log_simple("  Usuario: {$config['username']}");
    log_simple("  Base de datos: {$config['database']}");
    
    // Intentar conexión con manejo de errores
    try {
        // Desactivar reporte de errores mysqli temporalmente
        mysqli_report(MYSQLI_REPORT_OFF);
        
        // Intentar conexión
        $test_conn = @new mysqli(
            $config['host'],
            $config['username'],
            $config['password'],
            $config['database'],
            $config['port']
        );
        
        if ($test_conn->connect_error) {
            log_simple("  Error: " . $test_conn->connect_error);
            continue;
        }
        
        // Conexión exitosa
        log_simple("  ✅ CONEXIÓN EXITOSA!");
        $mysqli = $test_conn;
        $conn = $test_conn;
        $conexion = $test_conn;
        $db_connection_ok = true;
        $db_config_used = $config_name;
        break;
        
    } catch (Exception $e) {
        log_simple("  Excepción: " . $e->getMessage());
        continue;
    }
}

// Si no se pudo conectar, crear objeto dummy
if (!$db_connection_ok) {
    log_simple("❌ NO SE PUDO ESTABLECER CONEXIÓN CON NINGUNA CONFIGURACIÓN");
    $db_error_message = "No se pudo conectar a la base de datos";
    
    // Crear objeto dummy para evitar errores fatales
    class DummyMysqli {
        public $connect_error = "No hay conexión a la base de datos";
        public $error = "No hay conexión";
        public $errno = 0;
        
        public function query($sql) { return false; }
        public function prepare($sql) { return false; }
        public function real_escape_string($str) { return $str; }
        public function close() { return true; }
    }
    
    $mysqli = new DummyMysqli();
    $conn = $mysqli;
    $conexion = $mysqli;
}

// Función helper para crear conexión PDO
function createPDOConnection() {
    global $db_connection_ok, $db_configs, $db_config_used;
    
    if (!$db_connection_ok || !isset($db_configs[$db_config_used])) {
        return null;
    }
    
    $config = $db_configs[$db_config_used];
    
    try {
        $pdo = new PDO(
            "mysql:host={$config['host']};dbname={$config['database']};charset=utf8",
            $config['username'],
            $config['password'],
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]
        );
        return $pdo;
    } catch (PDOException $e) {
        log_simple("Error creando conexión PDO: " . $e->getMessage());
        return null;
    }
}

// Mostrar resumen del estado
log_simple("========================================");
log_simple("RESUMEN DE CONEXIÓN:");
log_simple("  Estado: " . ($db_connection_ok ? "CONECTADO" : "DESCONECTADO"));
log_simple("  Configuración usada: " . ($db_config_used ?: "Ninguna"));
log_simple("  Error: " . ($db_error_message ?: "Ninguno"));
log_simple("========================================");

// Solo mostrar información si se ejecuta directamente
if (basename($_SERVER['SCRIPT_FILENAME']) == basename(__FILE__)) {
    echo "<h2>Estado de Conexión a Base de Datos</h2>";
    echo "<pre>";
    echo "Ambiente: $ambiente\n";
    echo "Estado: " . ($db_connection_ok ? "✅ CONECTADO" : "❌ DESCONECTADO") . "\n";
    echo "Configuración: " . ($db_config_used ?: "Ninguna") . "\n";
    if (!$db_connection_ok) {
        echo "Error: $db_error_message\n";
    }
    echo "</pre>";
    
    echo "<h3>Configuraciones probadas:</h3>";
    echo "<ul>";
    foreach ($db_configs as $name => $config) {
        echo "<li>$name: {$config['host']}:{$config['port']}</li>";
    }
    echo "</ul>";
}
?>