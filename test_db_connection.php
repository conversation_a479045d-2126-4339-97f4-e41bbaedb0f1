<?php
// Archivo de prueba rápida de conexión - SIN AUTENTICACIÓN REQUERIDA
echo "<!DOCTYPE html>
<html>
<head>
    <title>Test de Conexión DB</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto; }
        h1, h2 { color: #333; }
        ul { line-height: 1.8; }
        a { color: #007bff; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>";

echo "<h1>Test de Conexión a Base de Datos</h1>";

// Incluir el archivo de conexión
require_once 'con_db.php';

echo "<h2>1. Variables de Estado</h2>";
echo "<pre>";
echo "db_connection_ok: " . ($db_connection_ok ? '<span class="success">true ✅</span>' : '<span class="error">false ❌</span>') . "\n";
echo "db_error_message: " . ($db_error_message ?: '<span class="success">Sin errores</span>') . "\n";
echo "db_config_used: " . ($db_config_used ?: '<span class="warning">Ninguna</span>') . "\n";
echo "</pre>";

echo "<h2>2. Tipo de Objeto mysqli</h2>";
echo "<pre>";
if ($mysqli instanceof mysqli) {
    echo '<span class="success">✅ Es una instancia válida de mysqli</span>';
} elseif ($mysqli instanceof DummyMysqli) {
    echo '<span class="warning">⚠️ Es un objeto DummyMysqli (sin conexión real)</span>';
} else {
    echo '<span class="error">❌ Tipo desconocido: ' . get_class($mysqli) . '</span>';
}
echo "</pre>";

echo "<h2>3. Test de Query Simple</h2>";
if ($db_connection_ok && $mysqli instanceof mysqli) {
    try {
        $result = $mysqli->query("SELECT 1 as test, NOW() as fecha_hora, DATABASE() as base_datos");
        if ($result) {
            echo "<p class='success'>✅ Query ejecutado exitosamente</p>";
            $row = $result->fetch_assoc();
            echo "<pre>";
            echo "Test: " . $row['test'] . "\n";
            echo "Fecha/Hora del servidor: " . $row['fecha_hora'] . "\n";
            echo "Base de datos actual: " . $row['base_datos'] . "\n";
            echo "</pre>";
        } else {
            echo "<p class='error'>❌ Error en query: " . $mysqli->error . "</p>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ Excepción al ejecutar query: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p class='warning'>⚠️ No hay conexión disponible para hacer queries</p>";
}

echo "<h2>4. Test de Conexión PDO</h2>";
try {
    $pdo = createPDOConnection();
    if ($pdo) {
        echo "<p class='success'>✅ Conexión PDO creada exitosamente</p>";
        $stmt = $pdo->query("SELECT VERSION() as version");
        $version = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<pre>MySQL Version: " . $version['version'] . "</pre>";
    } else {
        echo "<p class='warning'>⚠️ No se pudo crear conexión PDO</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Error al crear PDO: " . $e->getMessage() . "</p>";
}

echo "<h2>5. Información del Sistema</h2>";
echo "<pre>";
echo "PHP Version: " . phpversion() . "\n";
echo "Sistema Operativo: " . PHP_OS . "\n";
echo "Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "\n";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "\n";
echo "Script Path: " . $_SERVER['SCRIPT_FILENAME'] . "\n";
echo "HTTP Host: " . ($_SERVER['HTTP_HOST'] ?? 'Unknown') . "\n";
echo "</pre>";

echo "<h2>6. Extensiones PHP Relevantes</h2>";
echo "<pre>";
echo "mysqli: " . (extension_loaded('mysqli') ? '<span class="success">✅ Cargada</span>' : '<span class="error">❌ No disponible</span>') . "\n";
echo "pdo_mysql: " . (extension_loaded('pdo_mysql') ? '<span class="success">✅ Cargada</span>' : '<span class="error">❌ No disponible</span>') . "\n";
echo "mysqlnd: " . (extension_loaded('mysqlnd') ? '<span class="success">✅ Cargada</span>' : '<span class="error">❌ No disponible</span>') . "\n";
echo "</pre>";

echo "<h2>7. Enlaces Útiles</h2>";
echo "<ul>";
echo "<li><a href='con_db.php'>Ver diagnóstico completo de conexión</a></li>";
echo "<li><a href='login.php'>Volver al login</a></li>";
echo "<li><a href='logs/'>Ver carpeta de logs</a></li>";
echo "<li><a href='phpinfo.php'>Ver phpinfo()</a> (si existe)</li>";
echo "</ul>";

echo "<h2>8. Últimas líneas del log</h2>";
$log_file = __DIR__ . '/logs/db_connection.log';
if (file_exists($log_file)) {
    echo "<pre style='max-height: 300px; overflow-y: auto;'>";
    $lines = file($log_file);
    $recent_lines = array_slice($lines, -10);
    echo htmlspecialchars(implode('', $recent_lines));
    echo "</pre>";
} else {
    echo "<p class='warning'>⚠️ No se encontró el archivo de log</p>";
}

echo "</body></html>";
?>