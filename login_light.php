<?php
// Versión ligera del login para desarrollo - sin conexión a BD
?>
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="GESTAR INTRANET">
  <title>PORTAL GESTAR - LOGIN (MODO RÁPIDO)</title>
  
  <!-- CSS básico inline para velocidad -->
  <style>
    body {
      font-family: Arial, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      margin: 0;
      padding: 0;
      height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .login-container {
      background: white;
      padding: 40px;
      border-radius: 15px;
      box-shadow: 0 15px 35px rgba(0,0,0,0.1);
      width: 100%;
      max-width: 400px;
      text-align: center;
    }
    
    .logo {
      width: 80px;
      height: 80px;
      margin: 0 auto 20px;
      background: #667eea;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 24px;
      font-weight: bold;
    }
    
    h1 {
      color: #333;
      margin-bottom: 30px;
      font-size: 24px;
    }
    
    .form-group {
      margin-bottom: 20px;
      text-align: left;
    }
    
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
      color: #555;
    }
    
    input {
      width: 100%;
      padding: 12px;
      border: 2px solid #ddd;
      border-radius: 8px;
      font-size: 16px;
      box-sizing: border-box;
    }
    
    input:focus {
      border-color: #667eea;
      outline: none;
    }
    
    button {
      width: 100%;
      padding: 12px;
      background: #667eea;
      color: white;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      margin-top: 10px;
    }
    
    button:hover {
      background: #5a6fd8;
    }
    
    .message {
      padding: 10px;
      margin: 10px 0;
      border-radius: 5px;
      text-align: center;
    }
    
    .error { background: #ffebee; color: #c62828; border: 1px solid #ffcdd2; }
    .success { background: #e8f5e8; color: #2e7d32; border: 1px solid #c8e6c9; }
    .warning { background: #fff3e0; color: #ef6c00; border: 1px solid #ffccbc; }
    
    .fast-mode {
      background: #fff3e0;
      color: #ef6c00;
      padding: 8px;
      border-radius: 5px;
      margin-bottom: 20px;
      font-size: 14px;
    }
    
    .links {
      margin-top: 20px;
      font-size: 14px;
    }
    
    .links a {
      color: #667eea;
      text-decoration: none;
      margin: 0 10px;
    }
    
    .links a:hover {
      text-decoration: underline;
    }
  </style>
</head>

<body>
  <div class="login-container">
    <div class="logo">G</div>
    <h1>Portal Gestar</h1>
    
    <div class="fast-mode">
      🚀 Modo rápido - Sin conexión a BD
    </div>
    
    <div id="message-container"></div>
    
    <form id="login-form">
      <div class="form-group">
        <label for="rut">RUT o Email:</label>
        <input type="text" id="rut" name="rut" required>
      </div>
      
      <div class="form-group">
        <label for="clave">Contraseña:</label>
        <input type="password" id="clave" name="clave" required>
      </div>
      
      <button type="submit">Ingresar</button>
    </form>
    
    <div class="links">
      <a href="login.php">Modo completo</a> |
      <a href="test_db_connection.php">Test DB</a> |
      <a href="con_db.php">Diagnóstico</a>
    </div>
  </div>
  
  <script>
    function showMessage(type, text) {
      const container = document.getElementById('message-container');
      container.innerHTML = `<div class="message ${type}">${text}</div>`;
      setTimeout(() => {
        container.innerHTML = '';
      }, 5000);
    }
    
    document.getElementById('login-form').addEventListener('submit', function(e) {
      e.preventDefault();
      
      const rut = document.getElementById('rut').value;
      const clave = document.getElementById('clave').value;
      
      if (!rut || !clave) {
        showMessage('error', 'Por favor complete todos los campos');
        return;
      }
      
      // Simulación de login rápido
      showMessage('success', 'Login simulado exitoso - Redirigiendo...');
      
      setTimeout(() => {
        // Redirigir según el tipo de usuario (simulado)
        if (rut.includes('marcel') || rut.includes('admin')) {
          window.location.href = 'form_experian2.php';
        } else {
          window.location.href = 'form_experian.php';
        }
      }, 1000);
    });
    
    // Llenar campos de prueba automáticamente
    document.getElementById('rut').value = '<EMAIL>';
    document.getElementById('clave').value = 'test123';
  </script>
</body>
</html>