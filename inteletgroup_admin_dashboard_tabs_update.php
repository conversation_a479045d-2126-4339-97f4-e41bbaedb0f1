<?php
// ARCHIVO DE ACTUALIZACIÓN PARA REORGANIZAR LOS TABS EN inteletgroup_admin_dashboard.php
// Este archivo contiene las modificaciones necesarias para cambiar de 2 contenedores a 3 tabs

// PASO 1: REEMPLAZAR LA ESTRUCTURA HTML DEL PANEL LATERAL
// Buscar desde: <div class="sidebar-body">
// Hasta: </div> (el cierre de sidebar-body)

// NUEVA ESTRUCTURA HTML:
?>

<div class="sidebar-body">
    <!-- Tabs unificados -->
    <ul class="nav nav-tabs" id="documentTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="informacion-tab" data-bs-toggle="tab" 
                    data-bs-target="#informacion" type="button" role="tab">
                <i class="bi bi-person-circle me-2"></i>
                Información
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="documentos-tab" data-bs-toggle="tab" 
                    data-bs-target="#documentos" type="button" role="tab">
                <i class="bi bi-files me-2"></i>
                Documentos (<span id="docCount">0</span>)
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="checklist-tab" data-bs-toggle="tab" 
                    data-bs-target="#checklist" type="button" role="tab">
                <i class="bi bi-check2-square me-2"></i>
                Checklist
            </button>
        </li>
    </ul>
    
    <!-- Tab content -->
    <div class="tab-content mt-3" id="documentTabsContent">
        <!-- Tab de información -->
        <div class="tab-pane fade show active" id="informacion" role="tabpanel">
            <div class="prospecto-info-panel">
                <div class="info-panel-content">
                    <div class="row g-2">
                        <!-- Columna 1: Información Básica -->
                        <div class="col-4">
                            <div class="info-column">
                                <div class="info-item">
                                    <span class="info-label">ID:</span>
                                    <span id="sidebarProspectoId" class="info-value">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Usuario ID:</span>
                                    <span id="sidebarProspectoUsuarioId" class="info-value">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Nombre Ejecutivo:</span>
                                    <span id="sidebarProspectoEjecutivo" class="info-value">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">RUT Cliente:</span>
                                    <span id="sidebarProspectoRUT" class="info-value">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Razón Social:</span>
                                    <span id="sidebarProspectoRazonSocial" class="info-value">-</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Columna 2: Información de Negocio -->
                        <div class="col-4">
                            <div class="info-column">
                                <div class="info-item">
                                    <span class="info-label">Segmento:</span>
                                    <span id="sidebarProspectoSegmento" class="info-value">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Facturación:</span>
                                    <span id="sidebarProspectoFacturacion" class="info-value">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Estado:</span>
                                    <span id="sidebarProspectoEstado" class="info-value">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Tipo Persona:</span>
                                    <span id="sidebarProspectoTipoPersona" class="info-value badge">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Fecha Registro:</span>
                                    <span id="sidebarProspectoFecha" class="info-value">-</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Columna 3: Información de Contacto -->
                        <div class="col-4">
                            <div class="info-column">
                                <div class="info-item">
                                    <span class="info-label">Nombre Contacto:</span>
                                    <span id="sidebarProspectoContactoNombre" class="info-value">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Cargo:</span>
                                    <span id="sidebarProspectoContactoCargo" class="info-value">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Teléfono:</span>
                                    <span id="sidebarProspectoContactoTelefono" class="info-value">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Email:</span>
                                    <span id="sidebarProspectoContactoEmail" class="info-value">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Total Documentos:</span>
                                    <span id="sidebarProspectoTotalDocs" class="info-value">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Tab de documentos -->
        <div class="tab-pane fade" id="documentos" role="tabpanel">
            <div id="documentosContent" class="documents-container">
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Cargando...</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Tab de checklist -->
        <div class="tab-pane fade" id="checklist" role="tabpanel">
            <div id="checklistContent" class="checklist-container">
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Cargando...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// PASO 2: ACTUALIZAR LOS ESTILOS CSS
// Agregar estos estilos en la sección <style> del archivo
?>

<style>
/* Ajustes para el panel de información como tab */
#informacion .prospecto-info-panel {
    background: transparent;
    box-shadow: none;
    border: none;
    margin-bottom: 0;
    padding: 0;
}

#informacion .info-panel-content {
    padding: 1rem 0;
}

/* Ajustar el padding del sidebar-body ya que los tabs están más arriba */
.documents-sidebar .sidebar-body {
    padding: 1rem 1.5rem;
}

/* Mejorar la apariencia de los tabs */
.documents-sidebar .nav-tabs {
    border-bottom: 2px solid #e5e7eb;
    margin-bottom: 0;
}

.documents-sidebar .nav-tabs .nav-link {
    border-radius: 8px 8px 0 0;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.documents-sidebar .nav-tabs .nav-link.active {
    background: #f8f9fa;
    border: 1px solid #e5e7eb;
    border-bottom-color: #f8f9fa;
    margin-bottom: -2px;
}

/* Ajustar el contenido de los tabs */
.documents-sidebar .tab-content {
    background: #f8f9fa;
    border: 1px solid #e5e7eb;
    border-top: none;
    border-radius: 0 0 8px 8px;
    padding: 1rem;
    min-height: 400px;
    max-height: calc(100vh - 250px);
    overflow-y: auto;
}
</style>

<?php
// PASO 3: ACTUALIZAR EL JAVASCRIPT
// Modificar la función cargarDatosProspecto para no crear el panel de información dinámicamente
// ya que ahora está en el tab de información

// INSTRUCCIONES DE IMPLEMENTACIÓN:
// 1. Hacer backup del archivo original
// 2. Reemplazar la estructura HTML del sidebar-body con la nueva estructura de 3 tabs
// 3. Agregar los estilos CSS adicionales
// 4. Modificar el JavaScript para actualizar los datos en el tab de información
// 5. Probar que los 3 tabs funcionen correctamente
?>