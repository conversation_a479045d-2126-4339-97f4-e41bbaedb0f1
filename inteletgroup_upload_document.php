<?php
// Configuración de errores
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Log para debugging
error_log("=== INTELETGROUP_UPLOAD_DOCUMENT.PHP INICIADO ===");
error_log("REQUEST_METHOD: " . $_SERVER['REQUEST_METHOD']);
error_log("Usuario ID en sesión: " . ($_SESSION['usuario_id'] ?? 'No definido'));

// Headers
header('Content-Type: text/html; charset=utf-8');

session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    header('Location: login.php?error=' . urlencode('No autorizado'));
    exit;
}

require_once 'con_db.php';

$mensaje = '';
$tipo = 'danger';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['documento'])) {
    $prospecto_id = $_POST['prospecto_id'] ?? null;
    $rut_cliente = $_POST['rut_cliente'] ?? null;
    $tipo_documento_id = $_POST['tipo_documento_id'] ?? null;
    $usuario_id = $_SESSION['usuario_id'];
    
    error_log("=== PROCESANDO SUBIDA DE DOCUMENTO ===");
    error_log("Prospecto ID: $prospecto_id");
    error_log("RUT Cliente: $rut_cliente");
    error_log("Tipo Documento ID: $tipo_documento_id");
    error_log("Usuario ID: $usuario_id");
    error_log("Archivo: " . $_FILES['documento']['name'] . " (" . $_FILES['documento']['size'] . " bytes)");
    
    if (!$prospecto_id || !$rut_cliente) {
        $mensaje = 'Datos del prospecto no válidos';
        error_log("ERROR: Datos del prospecto no válidos");
    } else {
        $file = $_FILES['documento'];
        
        // Validaciones
        $allowed_types = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'image/jpeg',
            'image/jpg',
            'image/png',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];
        
        $max_size = 25 * 1024 * 1024; // 25MB
        
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $mensaje = 'Error al subir el archivo';
        } elseif (!in_array($file['type'], $allowed_types)) {
            $mensaje = 'Tipo de archivo no permitido';
        } elseif ($file['size'] > $max_size) {
            $mensaje = 'El archivo excede el tamaño máximo permitido (25MB)';
        } else {
            // Variable para rastrear si se reemplazó un documento
            $existing_id = null;
            
            // Verificar si ya existe un documento del mismo tipo para marcarlo como eliminado
            if ($tipo_documento_id) {
                error_log("Verificando documentos existentes para tipo: $tipo_documento_id");
                
                // Buscar documentos existentes del mismo tipo
                $check_stmt = $mysqli->prepare("
                    SELECT id, ruta_archivo 
                    FROM tb_inteletgroup_documentos 
                    WHERE prospecto_id = ? AND tipo_documento_id = ? AND estado = 'Activo'
                ");
                $check_stmt->bind_param("ii", $prospecto_id, $tipo_documento_id);
                $check_stmt->execute();
                
                $existing_path = null;
                $check_stmt->bind_result($existing_id, $existing_path);
                
                if ($check_stmt->fetch()) {
                    error_log("Documento existente encontrado - ID: $existing_id, Ruta: $existing_path");
                    $check_stmt->close();
                    
                    // Marcar el documento anterior como eliminado
                    $update_stmt = $mysqli->prepare("
                        UPDATE tb_inteletgroup_documentos 
                        SET estado = 'Eliminado', fecha_modificacion = NOW() 
                        WHERE id = ?
                    ");
                    $update_stmt->bind_param("i", $existing_id);
                    if ($update_stmt->execute()) {
                        error_log("Documento anterior marcado como eliminado (ID: $existing_id)");
                        
                        // Opcional: eliminar el archivo físico anterior
                        if (file_exists($existing_path)) {
                            if (unlink($existing_path)) {
                                error_log("Archivo físico anterior eliminado: $existing_path");
                            } else {
                                error_log("No se pudo eliminar archivo físico anterior: $existing_path");
                            }
                        }
                    }
                    $update_stmt->close();
                } else {
                    $check_stmt->close();
                    error_log("No se encontraron documentos existentes del tipo $tipo_documento_id");
                }
            }
            
            // Procesar archivo
            $upload_dir = 'uploads/inteletgroup_prospectos/';
            
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $unique_name = $rut_cliente . '_' . uniqid() . '.' . $extension;
            $file_path = $upload_dir . $unique_name;
            
            error_log("Intentando mover archivo a: $file_path");
            
            if (move_uploaded_file($file['tmp_name'], $file_path)) {
                error_log("Archivo movido exitosamente a: $file_path");
                
                // Insertar en base de datos
                $stmt = $mysqli->prepare("
                    INSERT INTO tb_inteletgroup_documentos (
                        prospecto_id, tipo_documento_id, usuario_id, rut_cliente, 
                        nombre_archivo, nombre_original, tipo_archivo, tamaño_archivo, ruta_archivo
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                if ($stmt) {
                    error_log("Preparando inserción en BD");
                    $stmt->bind_param("iiissssss",
                        $prospecto_id, $tipo_documento_id, $usuario_id, $rut_cliente,
                        $unique_name, $file['name'], $file['type'], $file['size'], $file_path
                    );
                    
                    if ($stmt->execute()) {
                        error_log("Documento insertado en BD exitosamente");
                        $documento_id = $mysqli->insert_id;
                        
                        // Actualizar checklist si se especificó tipo de documento
                        if ($tipo_documento_id) {
                            error_log("Actualizando checklist para tipo documento: $tipo_documento_id");
                            $stmt2 = $mysqli->prepare("
                                UPDATE tb_inteletgroup_documento_checklist 
                                SET documento_id = ?, estado = 'Subido' 
                                WHERE prospecto_id = ? AND tipo_documento_id = ?
                            ");
                            if ($stmt2) {
                                $stmt2->bind_param("iii", $documento_id, $prospecto_id, $tipo_documento_id);
                                $stmt2->execute();
                                error_log("Checklist actualizado. Filas afectadas: " . $stmt2->affected_rows);
                                $stmt2->close();
                            } else {
                                error_log("ERROR: No se pudo preparar la actualización del checklist");
                            }
                        }
                        
                        // Registrar en bitácora
                        $accion = isset($existing_id) && $existing_id ? 'Reemplazar Documento' : 'Subir Documento';
                        $stmt3 = $mysqli->prepare("
                            INSERT INTO tb_inteletgroup_prospecto_bitacora (
                                prospecto_id, usuario_id, accion, descripcion
                            ) VALUES (?, ?, ?, ?)
                        ");
                        $descripcion = "Documento subido: " . $file['name'] . " (Tipo ID: " . ($tipo_documento_id ?? 'No especificado') . ")";
                        if (isset($existing_id) && $existing_id) {
                            $descripcion .= " - Reemplazó documento ID: " . $existing_id;
                        }
                        $stmt3->bind_param("iiss", $prospecto_id, $usuario_id, $accion, $descripcion);
                        $stmt3->execute();
                        $stmt3->close();
                        
                        if (isset($existing_id) && $existing_id) {
                            $mensaje = 'Documento reemplazado exitosamente';
                        } else {
                            $mensaje = 'Documento subido exitosamente';
                        }
                        $tipo = 'success';
                        error_log("=== SUBIDA COMPLETADA EXITOSAMENTE ===");
                        error_log("Documento ID: $documento_id");
                        if (isset($existing_id) && $existing_id) {
                            error_log("Documento anterior eliminado - ID: $existing_id");
                        }
                    } else {
                        $mensaje = 'Error al guardar en la base de datos';
                        error_log("ERROR: No se pudo ejecutar la inserción - " . $stmt->error);
                        unlink($file_path); // Eliminar archivo si falla la BD
                    }
                    $stmt->close();
                } else {
                    $mensaje = 'Error al preparar la consulta';
                    error_log("ERROR: No se pudo preparar la consulta - " . $mysqli->error);
                    unlink($file_path);
                }
            } else {
                $mensaje = 'Error al mover el archivo al servidor';
                error_log("ERROR: No se pudo mover el archivo de " . $file['tmp_name'] . " a " . $file_path);
            }
        }
    }
}

// Redireccionar con mensaje
error_log("=== FINALIZANDO PROCESO DE SUBIDA ===");
error_log("Mensaje: $mensaje");
error_log("Tipo: $tipo");
error_log("Redirigiendo a: inteletgroup_documentos_enhanced.php?prospecto_id=" . ($prospecto_id ?? ''));

$_SESSION['upload_message'] = $mensaje;
$_SESSION['upload_type'] = $tipo;
header('Location: inteletgroup_documentos_enhanced.php?prospecto_id=' . ($prospecto_id ?? '') . '&mensaje=' . urlencode($mensaje) . '&tipo=' . $tipo);
exit;
?>