<?php
/**
 * Conexión rápida a la base de datos - versión optimizada para velocidad
 * SIN logging extensivo para evitar demoras en la carga
 */

// Solo mostrar errores críticos
error_reporting(E_ERROR | E_PARSE);
ini_set('display_errors', 0);

// Variables globales de estado
$db_connection_ok = false;
$db_error_message = '';
$db_config_used = '';

// Función de logging mínima (solo para errores críticos)
function log_error_only($mensaje) {
    @error_log("[DB FAST] $mensaje");
}

// Detectar ambiente de forma rápida
$is_local = (
    strpos($_SERVER['HTTP_HOST'] ?? '', 'localhost') !== false ||
    strpos($_SERVER['HTTP_HOST'] ?? '', '127.0.0.1') !== false ||
    strpos($_SERVER['DOCUMENT_ROOT'] ?? '', 'xampp') !== false
);

// Variables globales de conexión
$mysqli = null;
$conn = null;
$conexion = null;

// Configuración rápida según ambiente
if ($is_local) {
    // Para desarrollo local - conectar remotamente al servidor de hosting
    $configs = [
        // Conexión remota al servidor de hosting desde local
        ['gestarservicios.cl', 'gestarse_ncornejo7_experian', 'N1c0l7as17', 'gestarse_ncornejo7_experian', 3306],
        ['*************', 'gestarse_ncornejo7_experian', 'N1c0l7as17', 'gestarse_ncornejo7_experian', 3306],
        // Fallback a configuración local si existe
        ['127.0.0.1', 'root', '', 'gestarse_experian', 3306],
        ['localhost', 'root', '', 'gestarse_experian', 3306]
    ];
} else {
    // Para hosting - múltiples configuraciones para resolver problemas de host
    $configs = [
        // Configuración principal
        ['localhost', 'gestarse_ncornejo7_experian', 'N1c0l7as17', 'gestarse_ncornejo7_experian', 3306],
        // Configuración alternativa con IP local
        ['127.0.0.1', 'gestarse_ncornejo7_experian', 'N1c0l7as17', 'gestarse_ncornejo7_experian', 3306],
        // Configuración con el dominio completo
        ['gestarservicios.cl', 'gestarse_ncornejo7_experian', 'N1c0l7as17', 'gestarse_ncornejo7_experian', 3306],
        // Configuración con socket Unix (común en hosting compartido)
        ['localhost:/tmp/mysql.sock', 'gestarse_ncornejo7_experian', 'N1c0l7as17', 'gestarse_ncornejo7_experian', 3306]
    ];
}

// Probar conexiones con timeout muy bajo
foreach ($configs as $index => $config) {
    list($host, $user, $pass, $db, $port) = $config;
    
    try {
        mysqli_report(MYSQLI_REPORT_OFF);
        
        $test_conn = new mysqli();
        $test_conn->options(MYSQLI_OPT_CONNECT_TIMEOUT, 3); // Aumentar a 3 segundos para hosting
        
        // Intentar conexión
        if (@$test_conn->real_connect($host, $user, $pass, $db, $port)) {
            // Verificación rápida de que la conexión funciona
            if (@$test_conn->ping()) {
                $mysqli = $test_conn;
                $conn = $test_conn;
                $conexion = $test_conn;
                $db_connection_ok = true;
                $db_config_used = $is_local ? "local_config_$index" : "hosting_config_$index";
                log_error_only("Conexión exitosa con configuración $index: $host");
                break;
            }
        }
        
        if (isset($test_conn) && $test_conn->connect_error) {
            $db_error_message = "Config $index ($host): " . $test_conn->connect_error;
            log_error_only($db_error_message);
        }
        
        @$test_conn->close();
        
    } catch (Throwable $e) {
        $db_error_message = "Config $index ($host): " . $e->getMessage();
        log_error_only($db_error_message);
        continue;
    }
}

// Si no se conectó, crear objeto dummy
if (!$db_connection_ok) {
    log_error_only("No connection available after trying all configurations");
    
    class DummyMysqli {
        public $connect_error = "No database connection available";
        public $error = "No connection";
        public $errno = 0;
        public $connect_errno = 2002;
        
        public function query($sql) { return false; }
        public function prepare($sql) { return false; }
        public function real_escape_string($str) { return addslashes($str); }
        public function close() { return true; }
        public function ping() { return false; }
    }
    
    $mysqli = new DummyMysqli();
    $conn = $mysqli;
    $conexion = $mysqli;
    
    if (empty($db_error_message)) {
        $db_error_message = "Database connection failed - all configurations exhausted";
    }
}

// Función para crear conexión PDO (simplificada)
function createPDOConnection() {
    global $db_connection_ok, $is_local, $db_config_used, $mysqli;

    if (!$db_connection_ok) {
        return null;
    }

    try {
        if ($is_local) {
            // Para desarrollo local - usar conexión remota al hosting
            if (strpos($db_config_used, 'config_0') !== false) {
                // Configuración remota por dominio
                $dsn = "mysql:host=gestarservicios.cl;dbname=gestarse_ncornejo7_experian;charset=utf8";
                $user = 'gestarse_ncornejo7_experian';
                $pass = 'N1c0l7as17';
            } elseif (strpos($db_config_used, 'config_1') !== false) {
                // Configuración remota por IP
                $dsn = "mysql:host=*************;dbname=gestarse_ncornejo7_experian;charset=utf8";
                $user = 'gestarse_ncornejo7_experian';
                $pass = 'N1c0l7as17';
            } else {
                // Fallback a configuración local
                $dsn = "mysql:host=127.0.0.1;dbname=gestarse_experian;charset=utf8";
                $user = 'root';
                $pass = '';
            }
        } else {
            // Para hosting - usar la configuración que funcionó
            if (strpos($db_config_used, 'config_0') !== false) {
                $dsn = "mysql:host=localhost;dbname=gestarse_ncornejo7_experian;charset=utf8";
            } elseif (strpos($db_config_used, 'config_1') !== false) {
                $dsn = "mysql:host=127.0.0.1;dbname=gestarse_ncornejo7_experian;charset=utf8";
            } elseif (strpos($db_config_used, 'config_2') !== false) {
                $dsn = "mysql:host=gestarservicios.cl;dbname=gestarse_ncornejo7_experian;charset=utf8";
            } else {
                $dsn = "mysql:host=localhost;dbname=gestarse_ncornejo7_experian;charset=utf8";
            }
            $user = 'gestarse_ncornejo7_experian';
            $pass = 'N1c0l7as17';
        }

        return new PDO($dsn, $user, $pass, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_TIMEOUT => 5
        ]);

    } catch (PDOException $e) {
        log_error_only("PDO creation failed: " . $e->getMessage());
        return null;
    }
}

// Alias para compatibilidad
if (!function_exists('getDBConnection')) {
    function getDBConnection() {
        return createPDOConnection();
    }
}

// Función de cierre silenciosa
function cerrarConexion() {
    global $mysqli;
    if ($mysqli instanceof mysqli) {
        @$mysqli->close();
    }
}

register_shutdown_function('cerrarConexion');
?>