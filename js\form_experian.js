

// =================================================================================
// INICIO: Lógica de Interfaz de Usuario y Carga Inicial
// =================================================================================

// Verificar que jQuery y otras librerías se cargaron correctamente
window.onload = function() {
    if (typeof jQuery === "undefined") {
        console.error("jQuery no se cargó correctamente. Funcionalidades críticas pueden fallar.");
        alert("Error crítico: jQuery no se pudo cargar. Por favor, contacte a soporte.");
    } else {
        console.log("jQuery cargado correctamente: " + jQuery.fn.jquery);
    }
    if (typeof XLSX === "undefined") {
        console.error("Error: SheetJS (XLSX) no se cargó correctamente.");
    } else {
        console.log("SheetJS cargado correctamente.");
    }
    
    // Verificar el estado de la conexión a la base de datos
    if (typeof window.dbConnectionOk !== 'undefined' && !window.dbConnectionOk) {
        console.warn("Advertencia: No hay conexión a la base de datos. Funcionando en modo limitado.");
        console.warn("Error de conexión: " + (window.dbConnectionError || "Desconocido"));
        
        // Crear banner de advertencia si no existe ya
        if ($('#offline-mode-alert').length === 0) {
            $('body').prepend(
                '<div id="offline-mode-alert" style="background-color: #fff3cd; color: #856404; padding: 12px; margin: 0; ' +
                'text-align: center; border-bottom: 1px solid #ffeeba; font-weight: bold;">' +
                '<i class="fas fa-exclamation-triangle"></i> Modo sin conexión a la base de datos. ' +
                'Algunas funciones estarán limitadas.</div>'
            );
        }
        
        // Mostrar notificación al usuario
        if (typeof mostrarMensaje === 'function') {
            setTimeout(function() {
                mostrarMensaje("El sistema está funcionando en modo sin conexión. Algunas funciones estarán limitadas.", "warning", 10000);
            }, 1000);
        }
        
        // Intentar guardar en localStorage el estado de conexión
        try {
            localStorage.setItem('dbConnectionError', true);
            localStorage.setItem('dbConnectionErrorMessage', window.dbConnectionError || "Error de conexión desconocido");
            localStorage.setItem('dbConnectionErrorTime', new Date().toISOString());
        } catch (e) {
            console.warn("No se pudo guardar el estado de conexión en localStorage", e);
        }
    }
};

// Monitor de errores global para capturar y mostrar errores de JavaScript
window.addEventListener('error', function(event) {
    var errorMonitor = document.getElementById('error-monitor');
    var errorMessage = document.getElementById('error-message');
    var errorDetails = document.getElementById('error-details');
    
    if (errorMonitor && errorMessage && errorDetails) {
        errorMonitor.style.display = 'block';
        errorMessage.textContent = event.message || 'Error desconocido';
        
        var details = [
            'Archivo: ' + (event.filename || 'desconocido'),
            'Línea: ' + (event.lineno || '?') + ', Columna: ' + (event.colno || '?')
        ];
        if (event.error && event.error.stack) {
            details.push('\nStack Trace:\n' + event.error.stack);
        }
        
        errorDetails.textContent = details.join('\n');
    }
});

// Configuración de eventos de carga y error para todas las peticiones AJAX
$(document).ajaxStart(function() {
    $('#global-loader').fadeIn(300);
}).ajaxStop(function() {
    $('#global-loader').fadeOut(300);
}).ajaxError(function(event, jqXHR, ajaxSettings, thrownError) {
    var errorMonitor = document.getElementById('error-monitor');
    var errorMessage = document.getElementById('error-message');
    var errorDetails = document.getElementById('error-details');
    
    if (errorMonitor && errorMessage && errorDetails) {
        errorMonitor.style.display = 'block';
        errorMessage.textContent = 'Error en solicitud AJAX: ' + (thrownError || jqXHR.statusText || 'Error desconocido');
        
        var details = [
            'URL: ' + ajaxSettings.url,
            'Tipo: ' + ajaxSettings.type,
            'Código de estado: ' + (jqXHR.status || 'desconocido')
        ];
        
        if (jqXHR.responseText) {
            try {
                var jsonResponse = JSON.parse(jqXHR.responseText);
                details.push('\nRespuesta del servidor (JSON):\n' + JSON.stringify(jsonResponse, null, 2));
            } catch (e) {
                details.push('\nRespuesta del servidor:\n' + jqXHR.responseText.substring(0, 1000) + (jqXHR.responseText.length > 1000 ? '...' : ''));
            }
        }
        
        errorDetails.textContent = details.join('\n');
    }
});


// =================================================================================
// INICIO: Lógica de Prospectos y Bitácora
// =================================================================================

// Carga la tabla de prospectos desde el endpoint
function cargarEjecutivos(forzarRecarga = false) {
    // Verificar si hay conexión a la base de datos
    if (typeof window.dbConnectionOk !== 'undefined' && !window.dbConnectionOk) {
        console.warn('No hay conexión a la base de datos. No se pueden cargar prospectos.');
        $('#ejecutivos-table tbody').html('<tr><td colspan="11">No se pueden cargar datos porque no hay conexión a la base de datos.</td></tr>');
        return;
    }

    // Si ya tenemos datos precargados y no se fuerza recarga, usarlos directamente
    if (window.preloadedProspectsData && !forzarRecarga) {
        console.log('Usando datos de prospectos precargados');
        actualizarTablaProspectos(window.preloadedProspectsData);
        return;
    }

    // Si no hay datos precargados o se fuerza recarga, hacer petición AJAX
    $.ajax({
        url: 'endpoints/obtener_prospectos.php?t=' + new Date().getTime(),
        type: 'GET',
        dataType: 'json',
        cache: false,
        success: function(response) {
            actualizarTablaProspectos(response);
        },
        error: function(xhr, status, error) {
            console.error('Error al cargar prospectos:', error);
            $('#ejecutivos-table tbody').html('<tr><td colspan="11">Error al cargar datos. Por favor, intente nuevamente.</td></tr>');
            
            // Mostrar notificación al usuario
            if (typeof mostrarMensaje === 'function') {
                mostrarMensaje("Error al cargar datos de prospectos. Verifique su conexión.", "error", 5000);
            }
        }
    });
}

// Actualiza el contenido de la tabla de prospectos en el DOM
function actualizarTablaProspectos(data) {
  var tbody = $('#ejecutivos-table tbody');
  if (!data || data.length === 0) {
    tbody.html('<tr><td colspan="11" class="no-data">No hay datos disponibles</td></tr>');
    return;
  }

  var html = '';
  $.each(data, function(index, prospecto) {
    var estado = prospecto.ultimo_estado || prospecto.estado;
    var observaciones = prospecto.ultima_observacion || prospecto.observaciones || "";
    var fechaRegistro = prospecto.ultima_fecha_gestion || prospecto.fecha_registro;

    html += `
      <tr>
        <td><button class="btn-bitacora" data-rut="${prospecto.rut_ejecutivo}" data-nombre="${prospecto.nombre_ejecutivo}" data-razon="${prospecto.razon_social}"><i class="fa fa-book"></i> Bitácora</button></td>
        <td>${prospecto.nombre_ejecutivo}</td>
        <td>${prospecto.rut_ejecutivo}</td>
        <td>${prospecto.razon_social}</td>
        <td>${prospecto.rubro}</td>
        <td>${prospecto.contacto}</td>
        <td>${prospecto.telefono}</td>
        <td>${prospecto.fecha}</td>
        <td>${estado}</td>
        <td>${observaciones}</td>
        <td>${fechaRegistro}</td>
      </tr>`;
  });
  tbody.html(html);
}

// Carga el historial de la bitácora para un RUT específico
function cargarBitacora(rut) {
  if (!rut) return;
  
  // Verificar si hay conexión a la base de datos
  if (typeof window.dbConnectionOk !== 'undefined' && !window.dbConnectionOk) {
    console.warn('No hay conexión a la base de datos. No se puede cargar la bitácora.');
    $('#bitacora-timeline').html('<div class="error-data">No se puede cargar la bitácora porque no hay conexión a la base de datos.</div>');
    return;
  }
  
  $('#bitacora-timeline').html('<div class="loading-data">Cargando actividades...</div>');
  
  var flujoEstados = ["Envio información", "Negociación", "Cerrado", "B.O. Experian", "Proceso de Firma", "Firmado", "Habilitado"];

  $.ajax({
    url: 'endpoints/obtener_bitacora.php?rut=' + encodeURIComponent(rut),
    type: 'GET',
    dataType: 'json',
    cache: false,
    success: function(response) {
      if (response && response.success) {
        // Filtrar opciones del select de estado
        if (response.ultimo_estado) {
          var selectEstado = document.getElementById('bitacora_estado');
          if (selectEstado) {
            var indiceUltimo = flujoEstados.indexOf(response.ultimo_estado);
            if (indiceUltimo !== -1) {
              if (!selectEstado.dataset.opcionesOriginales) {
                selectEstado.dataset.opcionesOriginales = JSON.stringify(Array.from(selectEstado.options).map(opt => ({ value: opt.value, text: opt.text })));
              }
              selectEstado.innerHTML = '<option value="">Seleccione...</option>';
              for (var j = indiceUltimo; j < flujoEstados.length; j++) {
                var option = document.createElement('option');
                option.value = flujoEstados[j];
                option.text = flujoEstados[j];
                selectEstado.appendChild(option);
              }
            }
          }
        }

        // Renderizar timeline de bitácora
        if (!response.data || response.data.length === 0) {
          $('#bitacora-timeline').html('<div class="no-data">No hay registros disponibles</div>');
        } else {
          let html = '';
          $.each(response.data, function(index, registro) {
            let fechaRegistro = new Date(registro.fecha_registro);
            let fechaFormateada = fechaRegistro.toLocaleDateString('es-CL') + ' ' + fechaRegistro.toLocaleTimeString('es-CL', {hour: '2-digit', minute:'2-digit'});
            html += `
              <div class="timeline-item">
                <div class="timeline-content">
                  <div class="timeline-header">
                    <div class="timeline-text"><span class="highlight">${registro.estado}</span></div>
                    <div class="timeline-time">${fechaFormateada}</div>
                  </div>
                  <div class="timeline-subtext">${registro.observaciones}</div>
                  <div class="timeline-footer">
                    <div>Registrado por: <span class="highlight">${registro.nombre_usuario || 'N/A'}</span></div>
                  </div>
                </div>
              </div>`;
          });
          $('#bitacora-timeline').html(html);
        }
      } else {
        $('#bitacora-timeline').html('<div class="error-data">Error: ' + (response ? response.message : 'Respuesta inválida') + '</div>');
      }
    },
    error: function(xhr, status, error) {
      console.error('Error AJAX en cargarBitacora:', error);
      $('#bitacora-timeline').html('<div class="error-data">Error de conexión al cargar bitácora</div>');
      
      // Mostrar notificación al usuario
      if (typeof mostrarMensaje === 'function') {
        mostrarMensaje("Error al cargar la bitácora. Verifique su conexión.", "error", 5000);
      }
    }
  });
}


// =================================================================================
// INICIO: Lógica del Formulario Multi-paso
// =================================================================================

// Navega entre las secciones del formulario
function showFormSection(sectionNumber) {
    const seccionNum = parseInt(sectionNumber);
    if (isNaN(seccionNum) || seccionNum < 1 || seccionNum > 3) {
        console.error('Número de sección inválido:', sectionNumber);
        return;
    }

    $('#form-tab .section-container').removeClass('active');
    $('#form-tab #section' + seccionNum).addClass('active');

    $('#form-tab .step-indicator').removeClass('active');
    $('#form-tab .step-indicator[data-step="' + seccionNum + '"]').addClass('active');

    var percentage = ((seccionNum - 1) / 2) * 100;
    $('#form-tab .progress-line .fill').css('width', percentage + '%');

    manejarValidacionSecciones();
}

// Funciones para llenar automáticamente el formulario con datos de prueba
function generarRUT() {
    const rut = Math.floor(Math.random() * 20000000) + 5000000;
    const dv = calcularDV(rut);
    return formatearRUT(rut + '-' + dv);
}

function calcularDV(rut) {
    let suma = 0, multiplicador = 2;
    for (let i = rut.toString().length - 1; i >= 0; i--) {
        suma += parseInt(rut.toString().charAt(i)) * multiplicador;
        multiplicador = multiplicador === 7 ? 2 : multiplicador + 1;
    }
    const resto = suma % 11;
    const dv = 11 - resto;
    if (dv === 11) return '0';
    if (dv === 10) return 'K';
    return dv.toString();
}

function formatearRUT(rut) {
    const rutLimpio = rut.replace(/[^0-9kK]/g, '');
    const cuerpo = rutLimpio.slice(0, -1);
    const dv = rutLimpio.slice(-1);
    return cuerpo.replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1.') + '-' + dv;
}

function generarTelefono() { return '9' + Math.floor(Math.random() * 90000000 + 10000000); }
function generarEmail(nombre) { return nombre.toLowerCase().replace(/\s+/g, '.') + '@test.com'; }
function generarFecha(aniosAtras = 5) {
    const fecha = new Date();
    fecha.setFullYear(fecha.getFullYear() - Math.floor(Math.random() * aniosAtras));
    return fecha.toISOString().split('T')[0];
}

function llenarSeccion1() {
    const empresas = ['Comercial Los Andes S.A.', 'Distribuidora Central Ltda.', 'Servicios Tecnológicos del Sur S.A.'];
    const nombres = ['Juan Pérez', 'Ana Silva', 'Carlos López'];
    const comunas = ['Santiago', 'Las Condes', 'Providencia'];
    const empresa = empresas[Math.floor(Math.random() * empresas.length)];
    const nombreRep = nombres[Math.floor(Math.random() * nombres.length)];
    
    $('select[name="tipo_cliente"]').val('Cliente Vigente');
    $('input[name="rut"]').val(generarRUT());
    $('input[name="razon_social"]').val(empresa);
    $('input[name="nombre_representante1"]').val(nombreRep);
    $('input[name="rut_representante1"]').val(generarRUT());
    $('select[name="sistema_creacion"]').val('Tradicional');
    $('input[name="fecha_creacion"]').val(generarFecha(10));
    $('input[name="fecha_constitucion"]').val(generarFecha(15));
    $('input[name="direccion"]').val('Av. Principal 123');
    $('input[name="comuna"]').val(comunas[Math.floor(Math.random() * comunas.length)]);
    $('input[name="email"]').val(generarEmail(empresa.split(' ')[0]));
    $('input[name="telefono"]').val(generarTelefono());
    alert('Sección 1 llenada automáticamente.');
}

function llenarSeccion2() {
    const nombreContacto = 'Roberto Morales';
    $('input[name="contacto_nombre"]').val(nombreContacto);
    $('input[name="contacto_rut"]').val(generarRUT());
    $('input[name="contacto_telefono"]').val(generarTelefono());
    $('input[name="contacto_email"]').val(generarEmail(nombreContacto));
    alert('Sección 2 llenada automáticamente.');
}

function llenarSeccion3() {
    $('select[name="morosos_plan"]').val('M').trigger('change');
    $('select[name="morosos_descuento"]').val('10').trigger('change');
    $('select[name="advanced_plan"]').val('100').trigger('change');
    $('select[name="advanced_descuento"]').val('5').trigger('change');
    const nombreClave = 'Laura Torres';
    $('input[name="clave_nombre"]').val(nombreClave);
    $('input[name="clave_rut"]').val(generarRUT());
    $('input[name="clave_email"]').val(generarEmail(nombreClave));
    $('input[name="clave_telefono"]').val(generarTelefono());
    alert('Sección 3 llenada automáticamente.');
}

function llenarTodoElFormulario() {
    llenarSeccion1();
    setTimeout(() => { showFormSection(2); setTimeout(llenarSeccion2, 100); }, 200);
    setTimeout(() => { showFormSection(3); setTimeout(llenarSeccion3, 100); }, 400);
    setTimeout(() => alert('¡Formulario completo llenado!'), 600);
}


// =================================================================================
// INICIO: Sistema de Validación de Formularios
// =================================================================================

function validarRUT(rut) {
    if (!rut || rut.trim() === '') return false;
    const rutLimpio = rut.replace(/[^0-9kK]/g, '');
    if (rutLimpio.length < 8) return false;
    const cuerpo = rutLimpio.slice(0, -1);
    const dv = rutLimpio.slice(-1).toUpperCase();
    return calcularDV(cuerpo) === dv;
}
function validarEmail(email) { return /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(email.trim()); }
function validarTelefono(telefono) { return /^[0-9]{9}$/.test(telefono.replace(/[^0-9]/g, '')); }
function validarFecha(fecha, permitirFutura = false) {
    if (!fecha || fecha.trim() === '') return false;
    const fechaObj = new Date(fecha);
    if (isNaN(fechaObj.getTime())) return false;
    if (!permitirFutura && fechaObj > new Date()) return false;
    return true;
}

function mostrarErrorCampo(campo, mensaje) {
    $(campo).addClass('error-field').parent().find('.error-message').remove();
    $(campo).after(`<div class="error-message">${mensaje}</div>`);
}

function limpiarErrorCampo(campo) {
    $(campo).removeClass('error-field').parent().find('.error-message').remove();
}

function limpiarTodosLosErrores() {
    $('.error-field').each(function() { limpiarErrorCampo(this); });
    $('#error-summary').remove();
}

function mostrarResumenErrores(errores) {
    $('#error-summary').remove();
    const resumen = $('<div id="error-summary"><h4>Por favor, corrija los siguientes errores:</h4><ul></ul></div>');
    const lista = resumen.find('ul');
    errores.forEach(error => lista.append(`<li>${error}</li>`));
    $('#formExperian .container').prepend(resumen);
    resumen[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
}

function validarFormularioCompleto() {
    limpiarTodosLosErrores();
    const errores = [];
    let primerCampoConError = null;

    const check = (selector, validation, message, required = false) => {
        const campo = $(selector)[0];
        const value = $(campo).val();
        if ((required && !value.trim()) || (value.trim() && !validation(value))) {
            errores.push(message);
            mostrarErrorCampo(campo, message);
            if (!primerCampoConError) primerCampoConError = campo;
        }
    };

    // Sección 1
    check('select[name="tipo_cliente"]', val => val, 'Debe seleccionar el tipo de cliente', true);
    check('input[name="rut"]', validarRUT, 'El RUT de la empresa no es válido', true);
    check('input[name="razon_social"]', val => val, 'La razón social es requerida', true);
    check('input[name="nombre_representante1"]', val => val, 'El nombre del representante 1 es requerido', true);
    check('input[name="rut_representante1"]', validarRUT, 'El RUT del representante 1 no es válido');
    check('input[name="rut_representante2"]', validarRUT, 'El RUT del representante 2 no es válido');
    check('input[name="rut_representante3"]', validarRUT, 'El RUT del representante 3 no es válido');
    check('input[name="fecha_creacion"]', v => validarFecha(v), 'La fecha de creación es requerida y no puede ser futura', true);
    check('input[name="fecha_constitucion"]', v => validarFecha(v), 'La fecha de constitución es requerida y no puede ser futura', true);
    check('input[name="email"]', validarEmail, 'El correo electrónico no es válido', true);
    check('input[name="telefono"]', validarTelefono, 'El teléfono debe tener 9 dígitos', true);

    // Sección 2
    check('input[name="contacto_rut"]', validarRUT, 'El RUT del contacto no es válido');
    check('input[name="contacto_telefono"]', validarTelefono, 'El teléfono del contacto debe tener 9 dígitos');
    check('input[name="contacto_email"]', validarEmail, 'El email del contacto no es válido');
    
    // Sección 3
    check('input[name="clave_rut"]', validarRUT, 'El RUT para uso de claves no es válido');
    check('input[name="clave_email"]', validarEmail, 'El email para uso de claves no es válido');
    check('input[name="clave_telefono"]', validarTelefono, 'El teléfono para uso de claves debe tener 9 dígitos');

    if (errores.length > 0) {
        mostrarResumenErrores(errores);
        if (primerCampoConError) {
            setTimeout(() => {
                const sectionId = $(primerCampoConError).closest('.section-container').attr('id');
                if (sectionId) showFormSection(sectionId.replace('section', ''));
                primerCampoConError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                primerCampoConError.focus();
            }, 100);
        }
    }
    return { esValido: errores.length === 0, errores };
}

function validarArchivos() {
    const errores = [];
    const archivosPermitidos = ['pdf', 'jpg', 'jpeg', 'png'];
    const tamañoMaximo = 32 * 1024 * 1024; // 32MB

    $('input[type="file"]').each(function() {
        if (this.files && this.files.length > 0) {
            const archivo = this.files[0];
            const extension = archivo.name.split('.').pop().toLowerCase();
            if (!archivosPermitidos.includes(extension)) {
                errores.push(`Archivo ${archivo.name}: formato no permitido.`);
                mostrarErrorCampo(this, 'Formato no permitido');
            }
            if (archivo.size > tamañoMaximo) {
                errores.push(`Archivo ${archivo.name}: supera el tamaño máximo de 32MB.`);
                mostrarErrorCampo(this, 'Archivo muy grande');
            }
        }
    });
    return errores;
}

function configurarValidacionTiempoReal() {
    $('body').on('blur', '.rut-input', function() { if (this.value.trim() && !validarRUT(this.value)) mostrarErrorCampo(this, 'RUT inválido'); else limpiarErrorCampo(this); });
    $('body').on('blur', 'input[type="email"]', function() { if (this.value.trim() && !validarEmail(this.value)) mostrarErrorCampo(this, 'Email inválido'); else limpiarErrorCampo(this); });
    $('body').on('blur', 'input[type="tel"]', function() { if (this.value.trim() && !validarTelefono(this.value)) mostrarErrorCampo(this, 'Debe tener 9 dígitos'); else limpiarErrorCampo(this); });
    $('body').on('blur', 'input[type="date"]', function() { if (this.value.trim() && !validarFecha(this.value)) mostrarErrorCampo(this, 'Fecha no puede ser futura'); else limpiarErrorCampo(this); });
    $('body').on('blur', 'input[required], select[required]', function() { if (!this.value.trim()) mostrarErrorCampo(this, 'Campo requerido'); else limpiarErrorCampo(this); });
    $('body').on('input', '.error-field', function() { limpiarErrorCampo(this); });
}

function manejarValidacionSecciones() {
    $('.section-container:not(.active) [required]').each(function() {
        $(this).attr('data-was-required', 'true').removeAttr('required');
    });
    $('.section-container.active [data-was-required]').each(function() {
        $(this).attr('required', 'required').removeAttr('data-was-required');
    });
}

function restaurarValidacionHTML5() {
    $('[data-was-required]').each(function() {
        $(this).attr('required', 'required').removeAttr('data-was-required');
    });
}


// =================================================================================
// INICIO: Lógica de Eventos y Ejecución Principal
// =================================================================================

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM cargado. Configurando eventos.');

    // Cargar prospectos al inicio
    cargarEjecutivos();

    // Configurar validaciones en tiempo real
    configurarValidacionTiempoReal();
    manejarValidacionSecciones();

    // --- Eventos de Navegación y Modales ---
    $('body').on('click', '.btn-next', function() { showFormSection($(this).data('next')); });
    $('body').on('click', '.btn-prev', function() { showFormSection($(this).data('prev')); });
    $('body').on('click', '.btn-bitacora', function() {
        const rut = $(this).data('rut');
        $('#bitacora_rut').val(rut);
        $('#bitacora-info').html(`<strong>RUT:</strong> ${rut}`);
        cargarBitacora(rut);
        $('#bitacoraModal').css('display', 'block');
    });
    $('.modal .close-modal').on('click', function() { $(this).closest('.modal').css('display', 'none'); });
    $(window).on('click', function(event) {
        if ($(event.target).is('.modal')) {
            $(event.target).css('display', 'none');
        }
    });

    // --- Eventos de Formulario ---
    $('#bitacora-submit').on('click', function() {
        const rut = $('#bitacora_rut').val();
        const observaciones = $('#bitacora_observaciones').val();
        const estado = $('#bitacora_estado').val();
        if (!rut || !observaciones || !estado) {
            alert('Todos los campos de la bitácora son obligatorios.');
            return;
        }
        $.ajax({
            url: 'endpoints/agregar_bitacora.php',
            type: 'POST',
            data: { rut, observaciones, estado },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    $('#bitacora_observaciones').val('');
                    cargarBitacora(rut);
                    cargarEjecutivos(); // Recargar tabla principal
                    alert('Registro de bitácora agregado.');
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function() { alert('Error de conexión al guardar la bitácora.'); }
        });
    });

    $('#formExperian').on('submit', function(e) {
        e.preventDefault();
        restaurarValidacionHTML5();
        const { esValido } = validarFormularioCompleto();
        const erroresArchivos = validarArchivos();

        if (!esValido || erroresArchivos.length > 0) {
            if (erroresArchivos.length > 0) {
                const resumenErrores = $('#error-summary ul');
                if(resumenErrores.length > 0) {
                    erroresArchivos.forEach(err => resumenErrores.append(`<li>${err}</li>`));
                } else {
                    mostrarResumenErrores(erroresArchivos);
                }
            }
            return false;
        }

        var formData = new FormData(this);
        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                alert(response.message);
                if (response.success) {
                    $('#formExperian')[0].reset();
                    showFormSection(1);
                }
            },
            error: function() { alert('Error de conexión al guardar el formulario.'); }
        });
    });

    // --- Lógica de cálculo de planes ---
    const planesMorosos = { XS: { c: "5", uf: 1.5 }, S: { c: "10", uf: 2.5 }, M: { c: "20", uf: 4 }, L: { c: "40", uf: 6 }, XL: { c: "60", uf: 8 } };
    const planesAdvanced = { "25": { c: "0.35", uf: 8.75 }, "50": { c: "0.30", uf: 15 }, "100": { c: "0.25", uf: 25 }, "200": { c: "0.20", uf: 40 }, "300": { c: "0.18", uf: 54 }, "400": { c: "0.16", uf: 64 }, "500": { c: "0.15", uf: 75 }, "1000": { c: "0.13", uf: 130 }, "2000": { c: "0.11", uf: 220 }, "3000": { c: "0.10", uf: 300 }, "4000": { c: "0.09", uf: 360 }, "5000": { c: "0.08", uf: 400 } };

    function calcularPlan(plan, descuento, data, prefijo) {
        const planData = data[plan];
        if (planData) {
            const uf = parseFloat(planData.uf);
            const desc = parseFloat(descuento) / 100;
            const nuevoValor = uf * (1 - desc);
            $(`input[name="${prefijo}_consultas"]`).val(planData.c);
            $(`input[name="${prefijo}_uf"]`).val(uf.toFixed(2));
            $(`input[name="${prefijo}_nuevo_valor"]`).val(nuevoValor.toFixed(2));
        } else {
            $(`input[name^="${prefijo}_"]`).val("");
        }
    }

    $('#morosos_plan, #morosos_descuento').on('change', function() {
        calcularPlan($('#morosos_plan').val(), $('#morosos_descuento').val(), planesMorosos, 'morosos');
    });
    $('#advanced_plan, #advanced_descuento').on('change', function() {
        calcularPlan($('#advanced_plan').val(), $('#advanced_descuento').val(), planesAdvanced, 'advanced');
    });
    
    // Verificar que los botones existan
    var nextButtons = document.querySelectorAll('#form-tab .btn-next');
    var prevButtons = document.querySelectorAll('#form-tab .btn-prev');
    var stepIndicators = document.querySelectorAll('#form-tab .step-indicator');
    
    console.log('Inicializando navegación de formulario - Botones siguiente:', nextButtons.length);
    console.log('Botones anterior:', prevButtons.length);

    // Debug: mostrar información de los botones
    nextButtons.forEach(function(button, index) {
        console.log(`Botón siguiente ${index + 1}:`, {
            'data-next': button.getAttribute('data-next'),
            'class': button.className,
            'text': button.textContent.trim()
        });
    });

    prevButtons.forEach(function(button, index) {
        console.log(`Botón anterior ${index + 1}:`, {
            'data-prev': button.getAttribute('data-prev'),
            'class': button.className,
            'text': button.textContent.trim()
        });
    });
    
    // Función para mostrar una sección específica (global para evitar conflictos)
    window.showFormSection = function(sectionNumber) {
        // Convertir a número para evitar NaN
        const seccionNum = parseInt(sectionNumber);

        if (isNaN(seccionNum) || seccionNum < 1 || seccionNum > 3) {
            console.error('Número de sección inválido:', sectionNumber);
            return;
        }

        console.log('Cambiando a sección:', seccionNum);

        // Ocultar todas las secciones
        document.querySelectorAll('#form-tab .section-container').forEach(function(section) {
            section.classList.remove('active');
        });

        // Mostrar la sección seleccionada
        var targetSection = document.querySelector('#form-tab #section' + seccionNum);
        if (targetSection) {
            targetSection.classList.add('active');
        } else {
            console.error('No se encontró la sección:', seccionNum);
            return;
        }

        // Actualizar indicadores
        document.querySelectorAll('#form-tab .step-indicator').forEach(function(indicator) {
            indicator.classList.remove('active');
            if (indicator.getAttribute('data-step') === seccionNum.toString()) {
                indicator.classList.add('active');
            }
        });

        // Actualizar barra de progreso
        var progressFill = document.querySelector('#form-tab .progress-line .fill');
        if (progressFill) {
            var percentage = ((seccionNum - 1) / 2) * 100;
            progressFill.style.width = percentage + '%';
        }

        // Manejar validación de secciones después del cambio
        if (typeof manejarValidacionSecciones === 'function') {
            manejarValidacionSecciones();
        }
    };

    // Log para confirmar que la función global se definió
    console.log('Función showFormSection definida globalmente:', typeof window.showFormSection);
    
    // Configurar botones "Siguiente"
    nextButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            var nextSection = this.getAttribute('data-next');
            console.log('Botón siguiente clickeado, ir a sección:', nextSection);

            if (nextSection && nextSection !== '') {
                window.showFormSection(parseInt(nextSection));
            } else {
                console.error('No se encontró data-next en el botón');
            }
        });
    });

    // Configurar botones "Anterior"
    prevButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            var prevSection = this.getAttribute('data-prev');
            console.log('Botón anterior clickeado, ir a sección:', prevSection);

            if (prevSection && prevSection !== '') {
                window.showFormSection(parseInt(prevSection));
            } else {
                console.error('No se encontró data-prev en el botón');
            }
        });
    });
    
    // Configurar indicadores de paso
    stepIndicators.forEach(function(indicator) {
        indicator.addEventListener('click', function(e) {
            var stepNumber = this.getAttribute('data-step');
            console.log('Indicador clickeado, ir a sección:', stepNumber);
            window.showFormSection(stepNumber);
        });
    });

    // Inicializar mostrando la sección 1
    console.log('Inicializando formulario en sección 1');
    window.showFormSection(1);
    
    // Event listener para el botón de exportar registros
    document.addEventListener('click', function(e) {
        if (e.target && e.target.id === 'exportClients') {
            e.preventDefault();
            exportarClientesDirecto();
        }
    });

    // Obtener todos los botones del footer
    var footerTabs = document.querySelectorAll('.footer-tab');
    console.log('Tabs del footer encontrados:', footerTabs.length);

    // Configurar eventos de clic para cada botón
    footerTabs.forEach(function(tab) {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            var tabId = this.getAttribute('data-tab');
            console.log('Tab clickeado:', tabId);

            // Desactivar todos los tabs
            footerTabs.forEach(function(t) {
                t.classList.remove('active');
            });

            // Activar el tab actual
            this.classList.add('active');

            // Ocultar todos los contenidos
            var tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(function(content) {
                content.classList.remove('active');
            });

            // Mostrar el contenido correspondiente
            var activeContent = document.getElementById(tabId);
            if (activeContent) {
                activeContent.classList.add('active');
            }

            // Cargar datos si es necesario
            if (tabId === 'new-tab') {
                setTimeout(function() {
                    if (typeof cargarEjecutivos === 'function') {
                        cargarEjecutivos();
                    }
                }, 100);
            } else if (tabId === 'table-tab' && window.userIsAdmin) {
                setTimeout(function() {
                    if (typeof loadTableData === 'function') {
                        loadTableData();
                    } else {
                        console.log('Función loadTableData no disponible, los datos ya están cargados en el HTML');
                    }
                }, 100);
            }
        });
    });

    // Configurar búsqueda en tabla de prospectos
    var ejecutivosSearch = document.getElementById('ejecutivos-search');
    if (ejecutivosSearch) {
        console.log('Configurando búsqueda para tabla de prospectos');

        ejecutivosSearch.addEventListener('input', function() {
            var searchText = this.value.toLowerCase();
            console.log('Búsqueda en tabla de prospectos:', searchText);

            var rows = document.querySelectorAll('#ejecutivos-table tbody tr');
            rows.forEach(function(row) {
                var text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchText) ? '' : 'none';
            });
        });
    }

    // Configurar búsqueda en tabla de registros
    var tableSearch = document.getElementById('tableSearch');
    if (tableSearch) {
        console.log('Configurando búsqueda para tabla de registros');

        tableSearch.addEventListener('input', function() {
            var searchText = this.value.toLowerCase();
            console.log('Búsqueda en tabla de registros:', searchText);

            var rows = document.querySelectorAll('#user-table tbody tr');
            rows.forEach(function(row) {
                var text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchText) ? '' : 'none';
            });
        });
    }

    // Configurar botón de Nuevo Prospecto
    var openProspectoModal = document.getElementById('openProspectoModal');
    var prospectoModal = document.getElementById('prospectoModal');
    if (openProspectoModal && prospectoModal) {
        console.log('Configurando botón de Nuevo Prospecto');

        openProspectoModal.addEventListener('click', function() {
            console.log('Botón Nuevo Prospecto clickeado');
            prospectoModal.style.display = 'block';
        });

        // Cerrar el modal con el botón X
        var closeModalBtn = prospectoModal.querySelector('.close-modal');
        if (closeModalBtn) {
            closeModalBtn.addEventListener('click', function() {
                console.log('Cerrando modal');
                prospectoModal.style.display = 'none';
            });
        }

        // Cerrar el modal al hacer clic fuera de él
        window.addEventListener('click', function(event) {
            if (event.target === prospectoModal) {
                console.log('Cerrando modal (clic fuera)');
                prospectoModal.style.display = 'none';
            }
        });

        // Configurar formulario de prospectos
        var formEjecutivos = document.getElementById('formEjecutivos');
        if (formEjecutivos) {
            console.log('Configurando formulario de prospectos');

            // Eliminar cualquier manejador de eventos previo para evitar duplicación
            formEjecutivos.removeEventListener('submit', formEjecutivosSubmitHandler);

            // Definir el manejador de eventos como una función con nombre para poder eliminarlo
            function formEjecutivosSubmitHandler(e) {
                e.preventDefault();
                console.log('Formulario de prospectos enviado');

                if (!confirm('¿Está seguro de guardar este prospecto?')) {
                    return false;
                }

                // Mostrar indicador de carga
                $('<div class="loading-overlay"><div class="spinner"></div></div>').appendTo('body');

                // Obtener datos del formulario
                var formData = new FormData(this);

                // Convertir FormData a objeto para depuración
                var formDataObj = {};
                formData.forEach(function(value, key) {
                    formDataObj[key] = value;
                });
                console.log('Datos del formulario:', formDataObj);

                // Enviar mediante fetch para evitar problemas con jQuery
                fetch(formEjecutivos.getAttribute('action'), {
                    method: 'POST',
                    body: new URLSearchParams(new FormData(formEjecutivos))
                })
                .then(function(response) {
                    return response.json();
                })
                .then(function(data) {
                    console.log('Respuesta del servidor:', data);

                    // Eliminar indicador de carga
                    $('.loading-overlay').remove();

                    if (data.success) {
                        // Mostrar mensaje de éxito
                        alert(data.message);

                        // Cerrar el modal
                        prospectoModal.style.display = 'none';

                        // Limpiar el formulario
                        formEjecutivos.reset();

                        // Si el usuario tiene permisos, actualizar la tabla
                        if (window.userIsAdmin) {
                            setTimeout(function() {
                                if (typeof cargarEjecutivos === 'function') {
                                    cargarEjecutivos();
                                } else {
                                    // Recargar la página como alternativa
                                    window.location.reload();
                                }
                            }, 500);
                        }
                    } else {
                        // Mostrar mensaje de error
                        alert(data.message || 'Error al guardar el prospecto');
                    }
                })
                .catch(function(error) {
                    console.error('Error al enviar formulario:', error);
                    $('.loading-overlay').remove();
                    alert('Error al enviar el formulario. Revise la consola para más detalles.');
                });
            }

            // Agregar el manejador de eventos
            formEjecutivos.addEventListener('submit', formEjecutivosSubmitHandler);
        }
    }

    // Configurar botón de Descargar Prospectos
    var exportEjecutivos = document.getElementById('exportEjecutivos');
    if (exportEjecutivos) {
        console.log('Configurando botón de Descargar Prospectos');

        $("#exportEjecutivos").off('click').on("click", function(e) {
            e.preventDefault();
            console.log('Botón Descargar Prospectos clickeado');

            // Mostrar indicador de carga
            $('<div class="loading-overlay"><div class="spinner"></div></div>').appendTo('body');

            // Usar ruta relativa en lugar de absoluta
            $.ajax({
                url: 'endpoints/exportar_prospectos.php', // Asegúrate que esta ruta sea correcta
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        // Exportar a Excel
                        var wb = XLSX.utils.book_new();
                        var ws = XLSX.utils.json_to_sheet(response.data);
                        XLSX.utils.book_append_sheet(wb, ws, "Prospectos");
                        XLSX.writeFile(wb, "Prospectos_" + new Date().toISOString().slice(0,10) + ".xlsx");
                        alert("Archivo descargado correctamente");
                    } else {
                        alert("Error: " + response.message);
                    }
                    $('.loading-overlay').remove();
                },
                error: function(xhr, status, error) {
                    console.error("Error AJAX en exportación: ", xhr.responseText);
                    alert("Error al exportar: verifique la ruta del endpoint");
                    $('.loading-overlay').remove();
                }
            });
        });
    }

    // Configurar botones de bitácora - usar un ID único para este manejador de eventos
    if (!window.bitacoraButtonHandlerAdded) {
        window.bitacoraButtonHandlerAdded = true;
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('btn-bitacora') || e.target.closest('.btn-bitacora')) {
            var button = e.target.classList.contains('btn-bitacora') ? e.target : e.target.closest('.btn-bitacora');
            var rut = button.getAttribute('data-rut');
            var nombre = button.getAttribute('data-nombre');
            var razon = button.getAttribute('data-razon');

            console.log('Botón de bitácora clickeado para RUT:', rut);

            var bitacoraModal = document.getElementById('bitacoraModal');
            var bitacoraRut = document.getElementById('bitacora_rut');
            var bitacoraInfo = document.getElementById('bitacora-info');

            if (bitacoraModal && bitacoraRut && bitacoraInfo) {
                // Establecer información en el modal
                bitacoraRut.value = rut;
                bitacoraInfo.innerHTML = '<strong>Cliente:</strong> ' + razon + ' | <strong>Ejecutivo:</strong> ' + nombre + ' | <strong>RUT:</strong> ' + rut;

                // Cargar registros de bitácora
                if (typeof cargarBitacora === 'function') {
                    cargarBitacora(rut);
                } else {
                    // Implementación alternativa si la función no está disponible
                    console.log('Función cargarBitacora no disponible, implementando alternativa');

                    // Mostrar indicador de carga
                    $('#bitacora-timeline').html('<div class="loading-data">Cargando actividades...</div>');

                    // Cargar registros mediante AJAX con más información de depuración
                    console.log('Intentando cargar bitácora para RUT:', rut);

                    // Mostrar indicador de carga
                    $('#bitacora-timeline').html('<div class="loading-data">Cargando actividades...</div>');

                    // Construir la URL completa para depuración
                    var bitacoraUrl = 'endpoints/obtener_bitacora.php?rut=' + encodeURIComponent(rut);
                    console.log('URL de la bitácora:', bitacoraUrl);

                    // Definir el flujo de estados en orden
                    var flujoEstados = [
                        "Envio información",
                        "Negociación",
                        "Cerrado",
                        "B.O. Experian",
                        "Proceso de Firma",
                        "Firmado",
                        "Habilitado"
                    ];

                    $.ajax({
                        url: bitacoraUrl,
                        type: 'GET',
                        dataType: 'json',
                        cache: false,
                        beforeSend: function(xhr) {
                            console.log('Enviando solicitud AJAX...');
                        },
                        success: function(response) {
                            console.log('Respuesta recibida:', response);
                            if (response && response.success) {
                                // Filtrar las opciones del select según el último estado
                                if (response.ultimo_estado) {
                                  var selectEstado = document.getElementById('bitacora_estado');
                                  var indiceUltimo = flujoEstados.indexOf(response.ultimo_estado);

                                  if (indiceUltimo !== -1) {
                                    // Guardar todas las opciones originales si no lo hemos hecho antes
                                    if (!selectEstado.dataset.opcionesOriginales) {
                                      var opcionesOriginales = [];
                                      for (var i = 0; i < selectEstado.options.length; i++) {
                                        opcionesOriginales.push({
                                          value: selectEstado.options[i].value,
                                          text: selectEstado.options[i].text
                                        });
                                      }
                                      selectEstado.dataset.opcionesOriginales = JSON.stringify(opcionesOriginales);
                                    }

                                    // Limpiar el select
                                    selectEstado.innerHTML = '<option value="">Seleccione...</option>';

                                    // Agregar solo las opciones válidas (igual o posterior al último estado)
                                    for (var j = indiceUltimo; j < flujoEstados.length; j++) {
                                      var option = document.createElement('option');
                                      option.value = flujoEstados[j];
                                      option.text = flujoEstados[j];
                                      selectEstado.appendChild(option);
                                    }

                                    console.log('Select de estados filtrado según último estado:', response.ultimo_estado);
                                  }
                                } else {
                                  // Si no hay último estado, restaurar todas las opciones originales
                                  var selectEstado = document.getElementById('bitacora_estado');
                                  if (selectEstado.dataset.opcionesOriginales) {
                                    var opcionesOriginales = JSON.parse(selectEstado.dataset.opcionesOriginales);
                                    selectEstado.innerHTML = '<option value="">Seleccione...</option>';
                                    opcionesOriginales.forEach(function(opcion) {
                                      if (opcion.value) { // Ignorar la opción vacía
                                        var option = document.createElement('option');
                                        option.value = opcion.value;
                                        option.text = opcion.text;
                                        selectEstado.appendChild(option);
                                      }
                                    });
                                  }
                                }

                                if (response.data.length === 0) {
                                    $('#bitacora-timeline').html('<div class="no-data">No hay registros disponibles</div>');
                                    console.log('No hay registros disponibles');
                                } else {
                                    let html = '';
                                    $.each(response.data, function(index, registro) {
                                        // Determinar la clase de estado para el color del punto
                                        let estadoClass = 'estado-default';
                                        if (registro.estado.toLowerCase().includes('pendiente')) {
                                          estadoClass = 'estado-pendiente';
                                        } else if (registro.estado.toLowerCase().includes('proceso') || registro.estado.toLowerCase().includes('en curso')) {
                                          estadoClass = 'estado-en-proceso';
                                        } else if (registro.estado.toLowerCase().includes('completado') || registro.estado.toLowerCase().includes('finalizado')) {
                                          estadoClass = 'estado-completado';
                                        } else if (registro.estado.toLowerCase().includes('cancelado') || registro.estado.toLowerCase().includes('rechazado')) {
                                          estadoClass = 'estado-cancelado';
                                        }

                                        // Formatear la fecha para mostrar en formato corto
                                        let fechaRegistro = new Date(registro.fecha_registro);
                                        let fechaFormateada = fechaRegistro.toLocaleDateString('es-CL') + ' ' +
                                                            fechaRegistro.toLocaleTimeString('es-CL', {hour: '2-digit', minute:'2-digit'});

                                        html += '<div class="timeline-item ' + estadoClass + '">';
                                        html += '  <div class="timeline-content">';
                                        html += '    <div class="timeline-header">';
                                        html += '      <div class="timeline-text"><span class="highlight">' + registro.estado + '</span></div>';
                                        html += '      <div class="timeline-time">' + fechaFormateada + '</div>';
                                        html += '    </div>';
                                        html += '    <div class="timeline-subtext">' + registro.observaciones + '</div>';
                                        html += '    <div class="timeline-footer">';
                                        html += '      <div>Registrado por: <span class="highlight">' + (registro.nombre_usuario || 'N/A') + '</span></div>';
                                        html += '    </div>';
                                        html += '  </div>';
                                        html += '</div>';
                                    });
                                    $('#bitacora-timeline').html(html);
                                    console.log('Registros cargados correctamente:', response.data.length);
                                }
                            } else {
                                $('#bitacora-timeline').html('<div class="error-data">Error: ' + (response ? response.message : 'Respuesta inválida') + '</div>');
                                console.error('Error al cargar bitácora:', response ? response.message : 'Respuesta inválida');
                            }
                        },
                        error: function(xhr, status, error) {
                            $('#bitacora-timeline').html('<div class="error-data">Error de conexión: ' + status + '</div>');
                            console.error('Error AJAX al cargar bitácora:', {
                                status: status,
                                error: error,
                                response: xhr.responseText,
                                url: bitacoraUrl
                            });

                            // Intentar analizar la respuesta para obtener más información
                            try {
                                if (xhr.responseText) {
                                    var errorResponse = JSON.parse(xhr.responseText);
                                    console.error('Detalles del error:', errorResponse);
                                }
                            } catch (e) {
                                console.error('No se pudo analizar la respuesta de error:', xhr.responseText);
                            }
                        }
                    });
                }

                // Mostrar el modal
                bitacoraModal.style.display = 'block';

                // Configurar cierre del modal
                var closeBitacoraBtn = bitacoraModal.querySelector('.close-modal');
                if (closeBitacoraBtn) {
                    closeBitacoraBtn.addEventListener('click', function() {
                        console.log('Cerrando modal de bitácora');
                        bitacoraModal.style.display = 'none';
                    });
                }

                // Cerrar el modal al hacer clic fuera de él
                window.addEventListener('click', function(event) {
                    if (event.target === bitacoraModal) {
                        console.log('Cerrando modal de bitácora (clic fuera)');
                        bitacoraModal.style.display = 'none';
                    }
                });
            }
        }
    });
    }

    // Configurar formulario de bitácora
    var formBitacora = document.getElementById('formBitacora');
    if (formBitacora) {
        console.log('Configurando formulario de bitácora');

        // Eliminar todos los manejadores de eventos previos para evitar duplicación
        var formClone = formBitacora.cloneNode(true);
        formBitacora.parentNode.replaceChild(formClone, formBitacora);
        formBitacora = formClone;
        formBitacora.id = 'formBitacora';

        // Agregar un nuevo manejador de eventos
        $("#formBitacora").off('submit').on("submit", function(e) {
            e.preventDefault();
            var formData = $(this).serialize();
            var rutEjecutivo = $("#bitacora_rut").val();

            // Mostrar overlay
            $('<div class="loading-overlay"><div class="spinner"></div></div>').appendTo('body');

            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                data: formData,
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        alert(response.message);
                        $("#formBitacora")[0].reset();
                        $("#bitacoraModal").hide();
                        
                        // Llamar directamente a obtener_prospectos.php sin intermediarios
                        $.ajax({
                            url: 'endpoints/obtener_prospectos.php', 
                            type: 'GET',
                            dataType: 'json',
                            data: { t: new Date().getTime() }, // Evitar caché
                            success: function(tableData) {
                                console.log('ACTUALIZAR TABLA: Datos recibidos', tableData);
                                if (tableData.success) {
                                    actualizarTablaProspectos(tableData.data);
                                }
                            }
                        });
                    } else {
                        alert("Error: " + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Error:", xhr.responseText);
                    // Usar el sistema de notificaciones en lugar de alert
                    if (typeof mostrarMensaje === 'function') {
                        mostrarMensaje("Error al guardar el registro", "error", 6000);
                    } else {
                        alert("Error al guardar el registro");
                    }
                },
                complete: function() {
                    $('.loading-overlay').remove();
                }
            });
        });
    }

    // Función global para exportar clientes
    window.exportarClientesDirecto = function() {
        // Mostrar indicador de carga
        var loadingOverlay = document.createElement('div');
        loadingOverlay.className = 'loading-overlay';
        loadingOverlay.innerHTML = '<div class="spinner"></div><div class="loading-text">Exportando registros...</div>';
        document.body.appendChild(loadingOverlay);

        // Realizar petición al endpoint
        fetch('endpoints/exportar_clientes.php')
            .then(response => response.json())
            .then(function(data) {
                if (data.success) {
                    // Verificar que XLSX esté disponible
                    if (typeof XLSX === 'undefined') {
                        alert('Error: Librería XLSX no está cargada');
                        return;
                    }

                    // Exportar a Excel
                    var wb = XLSX.utils.book_new();
                    var ws = XLSX.utils.json_to_sheet(data.data);
                    XLSX.utils.book_append_sheet(wb, ws, "Registros_Clientes");
                    XLSX.writeFile(wb, "Registros_Clientes_" + new Date().toISOString().slice(0,10) + ".xlsx");
                    alert("Archivo descargado correctamente (" + data.total + " registros)");
                } else {
                    alert("Error: " + data.message);
                }
                document.body.removeChild(loadingOverlay);
            })
            .catch(function(error) {
                console.error("Error en exportación de clientes: ", error);
                alert("Error al exportar: " + error.message);
                if (loadingOverlay.parentNode) {
                    document.body.removeChild(loadingOverlay);
                }
            });
    };
    
    // Función para reemplazar alertas con notificaciones estilizadas
    function mostrarMensaje(mensaje, tipo, duracion) {
        // Crear elemento de notificación
        var notificacion = document.createElement('div');
        notificacion.className = 'notificacion ' + tipo;
        
        // Agregar ícono según el tipo
        var icono = '';
        if (tipo === 'success') {
            icono = '<i class="fa fa-check-circle"></i> ';
        } else if (tipo === 'error') {
            icono = '<i class="fa fa-exclamation-circle"></i> ';
        } else if (tipo === 'warning') {
            icono = '<i class="fa fa-exclamation-triangle"></i> ';
        } else if (tipo === 'info') {
            icono = '<i class="fa fa-info-circle"></i> ';
        }
        
        // Establecer contenido
        notificacion.innerHTML = icono + mensaje + '<span class="cerrar-notificacion">&times;</span>';
        
        // Agregar al DOM
        document.body.appendChild(notificacion);
        
        // Mostrar con animación
        setTimeout(function() {
            notificacion.classList.add('mostrar');
        }, 10);
        
        // Configurar cierre manual
        var cerrarBtn = notificacion.querySelector('.cerrar-notificacion');
        if (cerrarBtn) {
            cerrarBtn.addEventListener('click', function() {
                notificacion.classList.remove('mostrar');
                setTimeout(function() {
                    if (notificacion.parentNode) {
                        notificacion.parentNode.removeChild(notificacion);
                    }
                }, 300);
            });
        }
        
        // Auto-cerrar después de la duración especificada
        if (duracion) {
            setTimeout(function() {
                if (notificacion.parentNode) {
                    notificacion.classList.remove('mostrar');
                    setTimeout(function() {
                        if (notificacion.parentNode) {
                            notificacion.parentNode.removeChild(notificacion);
                        }
                    }, 300);
                }
            }, duracion);
        }
        
        return notificacion;
    }
    window.mostrarMensaje = mostrarMensaje;
    
    // Reemplazar la función de manejo de formulario para venta (formulario principal)
    $('#formExperian').off('submit').on('submit', function(e) {
        e.preventDefault();

        console.log('Formulario enviado - iniciando validación...');

        // Restaurar todos los campos required antes de la validación
        restaurarValidacionHTML5();

        // Deshabilitar temporalmente la validación HTML5 nativa para evitar errores en campos ocultos
        this.setAttribute('novalidate', 'novalidate');

        // Validación completa del formulario
        const resultadoValidacion = validarFormularioCompleto();

        if (!resultadoValidacion.esValido) {
            console.log('Validación falló:', resultadoValidacion.errores);
            // Rehabilitar validación HTML5 y manejar secciones
            this.removeAttribute('novalidate');
            manejarValidacionSecciones();
            return false;
        }

        // Validar archivos
        const erroresArchivos = validarArchivos();
        if (erroresArchivos.length > 0) {
            mostrarResumenErrores(erroresArchivos);
            // Rehabilitar validación HTML5 y manejar secciones
            this.removeAttribute('novalidate');
            manejarValidacionSecciones();
            return false;
        }

        // Confirmar antes de enviar
        if (!confirm('¿Está seguro de guardar este formulario?')) {
            // Rehabilitar validación HTML5 y manejar secciones
            this.removeAttribute('novalidate');
            manejarValidacionSecciones();
            return false;
        }
        
        // Deshabilitar botón de envío
        const submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true);
        
        // Mostrar indicador de carga
        $('<div class="loading-overlay"><div class="spinner"></div></div>').appendTo('body');
        
        // Usar FormData para incluir archivos
        var formData = new FormData(this);
        
        // Para debugging
        console.log('Enviando datos del formulario...');
        
        $.ajax({
            url: 'guardar_formulario.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                console.log('Respuesta recibida:', response);
                if (response.success) {
                    // Limpiar todos los errores de validación
                    limpiarTodosLosErrores();

                    // Usar el nuevo sistema de notificaciones en lugar de alert
                    mostrarMensaje(response.message, 'success', 6000);

                    // Limpiar el formulario
                    $('#formExperian')[0].reset();

                    // Volver a la primera sección
                    if (typeof showManualFormSection === 'function') {
                        showManualFormSection(1);
                    } else if (typeof showFormSection === 'function') {
                        showFormSection(1);
                    }

                    // Scroll al inicio del formulario
                    document.querySelector('#formExperian').scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                } else {
                    // Notificación de error
                    mostrarMensaje('Error: ' + response.message, 'error', 8000);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error al enviar formulario:', {
                    status: status,
                    error: error,
                    statusText: xhr.statusText,
                    responseText: xhr.responseText
                });
                
                // Verificar si hay respuesta del servidor
                let mensaje = 'Error al guardar los datos';
                
                try {
                    if (xhr.responseText) {
                        const jsonResponse = JSON.parse(xhr.responseText);
                        if (jsonResponse && jsonResponse.message) {
                            mensaje = jsonResponse.message;
                        }
                    }
                } catch (e) {
                    console.log('Error al parsear respuesta:', e);
                }
                
                // Mostrar notificación de error
                mostrarMensaje(mensaje, 'error', 8000);
            },
            complete: function() {
                // Habilitar botón de envío
                submitBtn.prop('disabled', false);
                // Quitar indicador de carga
                $('.loading-overlay').remove();
                // Restaurar validación HTML5 y manejar secciones
                $('#formExperian')[0].removeAttribute('novalidate');
                manejarValidacionSecciones();
            }
        });
    });
});
