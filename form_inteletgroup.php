<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Incluir utilidades de caché ANTES de cualquier salida
require_once 'cache_utils.php';

// LIMPIAR COMPLETAMENTE TODO EL CACHÉ
clear_all_cache();

// Configuración de encabezados HTTP - SIN CACHÉ TOTAL
header('Content-Type: text/html; charset=UTF-8');

// Aplicar headers ultra agresivos para eliminar TODO el caché
no_cache_headers();
dev_no_cache_headers();

// Configuraciones de sesión persistente (24 horas) - Estrategia TQW
ini_set('session.use_cookies', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_lifetime', 86400); // 24 horas
ini_set('session.gc_maxlifetime', 86400);  // 24 horas

// Iniciar sesión si no está activa
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verificar si venimos de navegación hacia atrás
$referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
if (strpos($referer, $_SERVER['HTTP_HOST']) !== false) {
    // Regenerar ID de sesión sin destruir datos
    session_regenerate_id(false);
}

// Establecer última actualización para ETag
$_SESSION['last_update'] = time();

// Inicializar variables importantes
$es_admin = false; // Variable que indica si el usuario es administrador

// Verificación mejorada de autenticación
function checkAuth() {
    if (!isset($_SESSION['usuario']) || empty($_SESSION['usuario'])) {
        // Registrar el intento fallido
        error_log("[" . date('Y-m-d H:i:s') . "] Error de autenticación - Usuario no identificado - IP: " . $_SERVER['REMOTE_ADDR']);

        // Redireccionar a login con mensaje de error
        $errorMsg = urlencode("Usuario no autenticado. Inicie sesión para continuar.");
        header('Location: ' . version_url('login.php?error=' . $errorMsg));
        exit;
    }

    // Renovar la sesión para evitar su caducidad prematura
    $_SESSION['last_activity'] = time();
    return true;
}

// Verificar si el usuario está autenticado
checkAuth();

// Verificar que el usuario pertenezca al proyecto InteletGroup
if (!isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    error_log("[" . date('Y-m-d H:i:s') . "] Acceso denegado - Usuario no pertenece al proyecto InteletGroup - Usuario: " . $_SESSION['usuario']);
    $errorMsg = urlencode("Acceso denegado. No tiene permisos para acceder a esta sección.");
    header('Location: ' . version_url('login.php?error=' . $errorMsg));
    exit;
}

// Incluir el archivo de conexión
require_once 'con_db.php';

// Obtener información del usuario logueado
$usuario_id = $_SESSION['usuario_id'];
$nombre_usuario = $_SESSION['nombre_usuario'] ?? 'Usuario';
$proyecto = $_SESSION['proyecto'] ?? 'inteletGroup';

// Verificar si el usuario es Oriana (ID = 4) para habilitar permisos especiales
$es_oriana = ($usuario_id == 4);

// Si es Oriana, obtener lista de ejecutivos para el selector
$ejecutivos = [];
if ($es_oriana) {
    $query_ejecutivos = "SELECT id, nombre_usuario FROM tb_experian_usuarios WHERE proyecto = 'inteletGroup' ORDER BY nombre_usuario";
    $result_ejecutivos = $mysqli->query($query_ejecutivos);
    
    if ($result_ejecutivos) {
        while ($row = $result_ejecutivos->fetch_assoc()) {
            $ejecutivos[] = $row;
        }
        $result_ejecutivos->free();
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>InteletGroup - Panel de Control - SIN CACHÉ</title>

    <!-- ANTI-CACHÉ TOTAL - NO GUARDAR NADA -->
    <?php echo no_cache_meta(); ?>

    <!-- JavaScript para limpiar caché del navegador -->
    <?php echo browser_cache_clear_js(); ?>

    <!-- Estilos con versiones anti-caché -->
    <link rel="stylesheet" href="<?php echo version_url('css/table-sort.css'); ?>">
    <link rel="stylesheet" href="<?php echo version_url('css/inteletgroup-prospect.css'); ?>">
    <link rel="stylesheet" href="<?php echo version_url('css/inteletgroup-checklist.css'); ?>">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
</head>
<body>
    <!-- Header profesional inspirado en la captura de pantalla -->
    <header class="simple-header">
        <div class="container">
            <div class="header-container">
                <!-- Logo y nombre del sitio -->
                <div class="brand-section">
                    <div class="logo-wrapper">
                        <img src="img/icons/logo_intelet.jpg" alt="Logo InteletGroup"
                             onerror="this.innerHTML='<i class=\'bi bi-building\' style=\'font-size: 1.5rem; color: white;\'></i>'">
                    </div>
                    <div class="site-info">
                        <h1 class="site-title">InteletGroup</h1>
                        <span class="site-subtitle">Panel Corporativo</span>
                    </div>
                </div>
                
                <!-- Usuario y acciones -->
                <div class="user-section">
                    <div class="user-profile-section">
                <?php if ($_SESSION['usuario_id'] == 4): ?>
                <a href="inteletgroup_admin_dashboard.php" class="header-action-btn me-2">
                    <i class="bi bi-speedometer2 me-1"></i> Dashboard Admin
                </a>
                <?php endif; ?>
                <span class="user-name-header me-2"><?php echo htmlspecialchars($nombre_usuario); ?></span>
                        <div class="user-role"><?php echo htmlspecialchars($proyecto); ?></div>
                    </div>
                    <a href="logout.php" class="logout-btn" title="Cerrar sesión" onclick="return confirm('¿Estás seguro de que quieres cerrar la sesión?');">
                        <i class="bi bi-box-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Welcome Card -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card welcome-card">
                    <div class="card-body text-center py-5">
                        <h2 class="card-title mb-3">
                            <i class="bi bi-rocket-takeoff me-2"></i>
                            Bienvenido a InteletGroup
                        </h2>
                        <p class="card-text lead">
                            Tu plataforma integral para la gestión de clientes y prospectos
                        </p>

                        <?php if (isset($_GET['dev']) && $_GET['dev'] === 'true'): ?>
                        <div class="mt-4">
                            <a href="limpiar_datos_prueba.php" class="btn btn-warning btn-sm">
                                <i class="bi bi-tools me-2"></i>
                                Herramientas de Desarrollo
                            </a>
                            <small class="d-block mt-2 text-light opacity-75">
                                Modo desarrollador activado
                            </small>
                        </div>
                        <?php endif; ?>

                    </div>
                </div>
            </div>
        </div>

           <!-- Information Alert -->
        <!-- <div class="row mt-5">
            <div class="col-12">
                <div class="alert alert-info" role="alert">
                    <h4 class="alert-heading">
                        <i class="bi bi-info-circle me-2"></i>
                        Información Importante
                    </h4>
                    <p>
                        Bienvenido al nuevo panel de InteletGroup. Estamos trabajando en el desarrollo de nuevas 
                        funcionalidades específicas para tu proyecto. 
                    </p>
                    <hr>
                    <p class="mb-0">
                        <strong>Fecha de lanzamiento estimada:</strong> Hoy<br>
                        <strong>Contacto para consultas:</strong> <EMAIL>
                    </p>
                </div>
            </div>
        </div> -->

        <!-- Features Grid -->
        <div class="row g-4">
            <!-- Registro de Prospectos -->
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card green-card">
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="bi bi-person-plus-fill"></i>
                        </div>
                        <h5 class="card-title fw-bold">Registro de Prospectos</h5>
                        <p class="card-text">
                            Registra nuevos prospectos comerciales con toda su información y documentación.
                        </p>
                        <button class="btn btn-success w-100" onclick="abrirModalInteletGroupProspecto()">
                            <i class="bi bi-plus-circle me-1"></i> Nuevo Prospecto
                        </button>
                    </div>
                </div>
            </div>

            <!-- Gestión de Documentos -->
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card blue-card">
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="bi bi-file-earmark-arrow-up"></i>
                        </div>
                        <h5 class="card-title fw-bold">Gestión de Documentos</h5>
                        <p class="card-text">
                            Sube y gestiona documentos de prospectos existentes por RUT.
                        </p>
                        <a href="inteletgroup_documentos_enhanced.php" class="btn btn-info w-100">
                            <i class="bi bi-files me-1"></i> Gestionar Docs
                        </a>
                    </div>
                </div>
            </div>

            <!-- Reportes -->
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card blue-card coming-soon">
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="bi bi-graph-up"></i>
                        </div>
                        <h5 class="card-title fw-bold">Reportes y Analytics</h5>
                        <p class="card-text">
                            Obtén insights valiosos con reportes detallados y análisis de datos.
                        </p>
                        <button class="btn btn-info w-100" disabled>
                            <i class="bi bi-arrow-right me-1"></i> Acceder
                        </button>
                    </div>
                </div>
            </div>

          

           
        </div>

     
    </div>

    <!-- Footer -->
    <footer class="mt-5 py-4 bg-light">
        <div class="container text-center">
            <p class="mb-0 text-muted">
                &copy; <?php echo date('Y'); ?> Gestar servicios. Todos los derechos reservados.
            </p>
        </div>
    </footer>

    <!-- Modal del formulario de prospectos -->
    <?php include 'inteletgroup_prospect_modal.html'; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- JavaScript simplificado del formulario de prospectos -->
    <script src="<?php echo version_url('js/inteletgroup-simple.js'); ?>"></script>

    <script>
        // Sistema de localStorage para navegación - Estrategia TQW
        (function() {
            // Mantener historial de navegación
            if (typeof localStorage !== "undefined") {
                var currentPage = window.location.href;
                var previousPages = JSON.parse(localStorage.getItem("navigationHistory") || "[]");
                
                // Añadir página actual si no existe
                if (previousPages.indexOf(currentPage) === -1) {
                    previousPages.push(currentPage);
                    // Mantener solo últimas 5 páginas
                    if (previousPages.length > 5) {
                        previousPages = previousPages.slice(-5);
                    }
                    localStorage.setItem("navigationHistory", JSON.stringify(previousPages));
                }
                
                // Registrar estado de sesión para diagnóstico
                localStorage.setItem('ultimaSesionActiva', '<?php echo date('Y-m-d H:i:s'); ?>');
                localStorage.setItem('ultimoUsuarioActivo', '<?php echo $_SESSION['usuario'] ?? ''; ?>');
                localStorage.setItem('ultimaPaginaConSesion', window.location.href);
            }
            
            // Configuración AJAX mejorada
            if (window.jQuery) {
                $.ajaxSetup({
                    cache: false // Solo para peticiones AJAX
                });
            }
        })();
        
        // Variables globales para el formulario
        window.currentUserName = '<?php echo addslashes($nombre_usuario); ?>';
        window.currentUserId = <?php echo $usuario_id; ?>;
        window.esOriana = <?php echo $es_oriana ? 'true' : 'false'; ?>;
        
        // Lista de ejecutivos disponibles si es Oriana
        <?php if ($es_oriana): ?>
        window.ejecutivosDisponibles = <?php echo json_encode($ejecutivos); ?>;
        <?php else: ?>
        window.ejecutivosDisponibles = [];
        <?php endif; ?>
        
        // Registro de versión sin forzar recarga - Estrategia TQW
        (function() {
            // Solo registrar para diagnóstico, sin forzar recargas
            if (typeof localStorage !== 'undefined') {
                localStorage.setItem('inteletgroup_ultima_visita', '<?php echo date("Y-m-d H:i:s"); ?>');
            }
        })();

        // Log del proyecto para debugging
        console.log('InteletGroup Panel - Usuario:', '<?php echo htmlspecialchars($nombre_usuario); ?>');
        console.log('Proyecto:', '<?php echo htmlspecialchars($proyecto); ?>');
        console.log('Usuario ID:', <?php echo $usuario_id; ?>);
        console.log('Versión de página:', '<?php echo date("Y-m-d H:i:s"); ?>');

        // DESREGISTRAR SERVICE WORKER para evitar bucles infinitos
        function unregisterServiceWorker() {
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistrations().then(function(registrations) {
                    registrations.forEach(function(registration) {
                        console.log('🚫 Desregistrando Service Worker:', registration.scope);
                        registration.unregister().then(function(success) {
                            if (success) {
                                console.log('✅ Service Worker desregistrado exitosamente');
                            }
                        }).catch(function(error) {
                            console.warn('⚠️ Error al desregistrar Service Worker:', error);
                        });
                    });
                }).catch(function(error) {
                    console.warn('⚠️ Error al obtener registros de Service Worker:', error);
                });
            }
        }

        // Mensaje de bienvenida y inicialización
        document.addEventListener('DOMContentLoaded', function() {
            unregisterServiceWorker(); // Desregistrar Service Worker primero
            console.log('Panel de InteletGroup cargado correctamente');

            // Deshabilitar caché para peticiones AJAX
            $.ajaxSetup({
                cache: false
            });
        });
    </script>

    <!-- Script anti-caché MÍNIMO para evitar bucles -->
    <script src="<?php echo version_url('js/no-cache-minimal.js'); ?>"></script>
</body>
</html>
