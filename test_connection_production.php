<?php
/**
 * Script de diagnóstico para conexión de base de datos en producción
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Diagnóstico de Conexión - Producción</h2>";
echo "<p>Servidor: " . $_SERVER['HTTP_HOST'] . "</p>";
echo "<p>Documento Root: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<hr>";

// Configuraciones a probar
$configs = [
    ['localhost', 'gestarse_ncornejo7_experian', 'N1c0l7as17', 'gestarse_ncornejo7_experian', 3306],
    ['127.0.0.1', 'gestarse_ncornejo7_experian', 'N1c0l7as17', 'gestarse_ncornejo7_experian', 3306],
    ['gestarservicios.cl', 'gestarse_ncornejo7_experian', 'N1c0l7as17', 'gestarse_ncornejo7_experian', 3306],
    ['localhost:/tmp/mysql.sock', 'gestarse_ncornejo7_experian', 'N1c0l7as17', 'gestarse_ncornejo7_experian', 3306]
];

foreach ($configs as $index => $config) {
    list($host, $user, $pass, $db, $port) = $config;
    
    echo "<h3>Configuración $index: $host</h3>";
    
    try {
        mysqli_report(MYSQLI_REPORT_OFF);
        
        $test_conn = new mysqli();
        $test_conn->options(MYSQLI_OPT_CONNECT_TIMEOUT, 5);
        
        $start_time = microtime(true);
        
        if (@$test_conn->real_connect($host, $user, $pass, $db, $port)) {
            $end_time = microtime(true);
            $connection_time = round(($end_time - $start_time) * 1000, 2);
            
            echo "✅ <strong>CONEXIÓN EXITOSA</strong><br>";
            echo "⏱️ Tiempo de conexión: {$connection_time}ms<br>";
            
            // Probar una consulta simple
            $result = $test_conn->query("SELECT COUNT(*) as total FROM tb_experian_usuarios");
            if ($result) {
                $row = $result->fetch_assoc();
                echo "📊 Usuarios en la tabla: " . $row['total'] . "<br>";
            }
            
            echo "🔧 Versión MySQL: " . $test_conn->server_info . "<br>";
            echo "🏠 Host info: " . $test_conn->host_info . "<br>";
            
            $test_conn->close();
            echo "<p style='color: green;'>Esta configuración funciona correctamente.</p>";
            break; // Salir del bucle si encontramos una conexión exitosa
            
        } else {
            echo "❌ <strong>ERROR DE CONEXIÓN</strong><br>";
            echo "Error: " . $test_conn->connect_error . "<br>";
            echo "Código de error: " . $test_conn->connect_errno . "<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ <strong>EXCEPCIÓN</strong><br>";
        echo "Error: " . $e->getMessage() . "<br>";
    }
    
    echo "<hr>";
}

// Información adicional del sistema
echo "<h3>Información del Sistema</h3>";
echo "PHP Version: " . phpversion() . "<br>";
echo "MySQL Extension: " . (extension_loaded('mysqli') ? 'Disponible' : 'No disponible') . "<br>";
echo "PDO MySQL: " . (extension_loaded('pdo_mysql') ? 'Disponible' : 'No disponible') . "<br>";

// Verificar permisos de archivos
echo "<h3>Permisos de Archivos</h3>";
$files_to_check = ['con_db_fast.php', 'login.php', 'ControllerGestar.php'];
foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        echo "$file: " . substr(sprintf('%o', $perms), -4) . "<br>";
    } else {
        echo "$file: No encontrado<br>";
    }
}
?>