
[10-Jul-2025 21:43:13 America/Santiago] Filtro ejecutivo: todos
[10-Jul-2025 21:43:13 America/Santiago] Filtro fecha inicio: 2025-01-01
[10-Jul-2025 21:43:13 America/Santiago] Filtro fecha fin: 2025-12-31
[10-Jul-2025 21:43:13 America/Santiago] INFO: Prospectos obtenidos: 5
[11-Jul-2025 01:43:15 UTC] === INTELETGROUP_ADMIN_DASHBOARD.PHP INICIADO ===
[10-Jul-2025 21:43:15 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 249
[10-Jul-2025 21:43:15 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 250
[10-Jul-2025 21:43:15 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 253
[10-Jul-2025 21:43:15 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 254
[10-Jul-2025 21:43:15 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 257
[10-Jul-2025 21:43:15 America/Santiago] DEBUG: Conexión a BD establecida correctamente
[10-Jul-2025 21:43:15 America/Santiago] DEBUG DASHBOARD: Usuario ID=4, Nombre BD='Oriana Camilo', Proyecto BD='inteletGroup'
[10-Jul-2025 21:43:15 America/Santiago] DEBUG DASHBOARD: Sesión nombre='Oriana Camilo', Sesión proyecto='inteletGroup'
[10-Jul-2025 21:43:15 America/Santiago] DEBUG: Obteniendo lista de ejecutivos...
[10-Jul-2025 21:43:15 America/Santiago] INFO: Filtros - Ejecutivo: todos, Periodo: año
[10-Jul-2025 21:43:15 America/Santiago] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[10-Jul-2025 21:43:15 America/Santiago] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[10-Jul-2025 21:43:15 America/Santiago] DEBUG: Ejecutando consulta de prospectos: 
    SELECT COUNT(*) as total_prospectos
    FROM tb_inteletgroup_prospectos p
    INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    WHERE u.proyecto = 'inteletGroup' AND DATE(p.fecha_registro) BETWEEN ? AND ?

[10-Jul-2025 21:43:15 America/Santiago] INFO: Total prospectos obtenido: 5
[10-Jul-2025 21:43:15 America/Santiago] INFO: Total documentos obtenido: 23
[10-Jul-2025 21:43:15 America/Santiago] INFO: Completitud promedio: 46.7
[10-Jul-2025 21:43:15 America/Santiago] DEBUG: Obteniendo prospectos por tipo de persona...
[10-Jul-2025 21:43:15 America/Santiago] DEBUG: Tipos encontrados: Array
(
    [Natural] => 3
    [Juridica] => 2
)

[10-Jul-2025 21:43:15 America/Santiago] INFO: Prospectos por ejecutivo: 4
[10-Jul-2025 21:43:15 America/Santiago] INFO: Datos de evolución obtenidos: 2
[10-Jul-2025 21:43:15 America/Santiago] INFO: Ejecutando consulta de prospectos
[10-Jul-2025 21:43:15 America/Santiago] Query: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.fecha_registro,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, p.telefono_celular, p.fecha_registro, u.nombre_usuario
    ORDER BY p.fecha_registro DESC

[10-Jul-2025 21:43:15 America/Santiago] Filtro ejecutivo: todos
[10-Jul-2025 21:43:15 America/Santiago] Filtro fecha inicio: 2025-01-01
[10-Jul-2025 21:43:15 America/Santiago] Filtro fecha fin: 2025-12-31
[10-Jul-2025 21:43:15 America/Santiago] INFO: Prospectos obtenidos: 5
[11-Jul-2025 01:43:17 UTC] === INTELETGROUP_ADMIN_DASHBOARD.PHP INICIADO ===
[10-Jul-2025 21:43:17 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 249
[10-Jul-2025 21:43:17 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 250
[10-Jul-2025 21:43:17 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 253
[10-Jul-2025 21:43:17 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 254
[10-Jul-2025 21:43:17 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 257
[10-Jul-2025 21:43:17 America/Santiago] DEBUG: Conexión a BD establecida correctamente
[10-Jul-2025 21:43:17 America/Santiago] DEBUG DASHBOARD: Usuario ID=4, Nombre BD='Oriana Camilo', Proyecto BD='inteletGroup'
[10-Jul-2025 21:43:17 America/Santiago] DEBUG DASHBOARD: Sesión nombre='Oriana Camilo', Sesión proyecto='inteletGroup'
[10-Jul-2025 21:43:17 America/Santiago] DEBUG: Obteniendo lista de ejecutivos...
[10-Jul-2025 21:43:17 America/Santiago] INFO: Filtros - Ejecutivo: todos, Periodo: año
[10-Jul-2025 21:43:17 America/Santiago] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[10-Jul-2025 21:43:17 America/Santiago] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[10-Jul-2025 21:43:17 America/Santiago] DEBUG: Ejecutando consulta de prospectos: 
    SELECT COUNT(*) as total_prospectos
    FROM tb_inteletgroup_prospectos p
    INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    WHERE u.proyecto = 'inteletGroup' AND DATE(p.fecha_registro) BETWEEN ? AND ?

[10-Jul-2025 21:43:17 America/Santiago] INFO: Total prospectos obtenido: 5
[10-Jul-2025 21:43:17 America/Santiago] INFO: Total documentos obtenido: 23
[10-Jul-2025 21:43:17 America/Santiago] INFO: Completitud promedio: 46.7
[10-Jul-2025 21:43:17 America/Santiago] DEBUG: Obteniendo prospectos por tipo de persona...
[10-Jul-2025 21:43:17 America/Santiago] DEBUG: Tipos encontrados: Array
(
    [Natural] => 3
    [Juridica] => 2
)

[10-Jul-2025 21:43:17 America/Santiago] INFO: Prospectos por ejecutivo: 4
[10-Jul-2025 21:43:17 America/Santiago] INFO: Datos de evolución obtenidos: 2
[10-Jul-2025 21:43:17 America/Santiago] INFO: Ejecutando consulta de prospectos
[10-Jul-2025 21:43:17 America/Santiago] Query: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.fecha_registro,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, p.telefono_celular, p.fecha_registro, u.nombre_usuario
    ORDER BY p.fecha_registro DESC

[10-Jul-2025 21:43:17 America/Santiago] Filtro ejecutivo: todos
[10-Jul-2025 21:43:17 America/Santiago] Filtro fecha inicio: 2025-01-01
[10-Jul-2025 21:43:17 America/Santiago] Filtro fecha fin: 2025-12-31
[10-Jul-2025 21:43:17 America/Santiago] INFO: Prospectos obtenidos: 5
[11-Jul-2025 01:43:19 UTC] === INTELETGROUP_ADMIN_DASHBOARD.PHP INICIADO ===
[10-Jul-2025 21:43:19 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 249
[10-Jul-2025 21:43:19 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 250
[10-Jul-2025 21:43:19 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 253
[10-Jul-2025 21:43:19 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 254
[10-Jul-2025 21:43:19 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 257
[10-Jul-2025 21:43:19 America/Santiago] DEBUG: Conexión a BD establecida correctamente
[10-Jul-2025 21:43:19 America/Santiago] DEBUG DASHBOARD: Usuario ID=4, Nombre BD='Oriana Camilo', Proyecto BD='inteletGroup'
[10-Jul-2025 21:43:19 America/Santiago] DEBUG DASHBOARD: Sesión nombre='Oriana Camilo', Sesión proyecto='inteletGroup'
[10-Jul-2025 21:43:19 America/Santiago] DEBUG: Obteniendo lista de ejecutivos...
[10-Jul-2025 21:43:19 America/Santiago] INFO: Filtros - Ejecutivo: todos, Periodo: año
[10-Jul-2025 21:43:19 America/Santiago] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[10-Jul-2025 21:43:19 America/Santiago] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[10-Jul-2025 21:43:19 America/Santiago] DEBUG: Ejecutando consulta de prospectos: 
    SELECT COUNT(*) as total_prospectos
    FROM tb_inteletgroup_prospectos p
    INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    WHERE u.proyecto = 'inteletGroup' AND DATE(p.fecha_registro) BETWEEN ? AND ?

[10-Jul-2025 21:43:19 America/Santiago] INFO: Total prospectos obtenido: 5
[10-Jul-2025 21:43:19 America/Santiago] INFO: Total documentos obtenido: 23
[10-Jul-2025 21:43:19 America/Santiago] INFO: Completitud promedio: 46.7
[10-Jul-2025 21:43:19 America/Santiago] DEBUG: Obteniendo prospectos por tipo de persona...
[10-Jul-2025 21:43:19 America/Santiago] DEBUG: Tipos encontrados: Array
(
    [Natural] => 3
    [Juridica] => 2
)

[10-Jul-2025 21:43:19 America/Santiago] INFO: Prospectos por ejecutivo: 4
[10-Jul-2025 21:43:19 America/Santiago] INFO: Datos de evolución obtenidos: 2
[10-Jul-2025 21:43:19 America/Santiago] INFO: Ejecutando consulta de prospectos
[10-Jul-2025 21:43:19 America/Santiago] Query: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.fecha_registro,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, p.telefono_celular, p.fecha_registro, u.nombre_usuario
    ORDER BY p.fecha_registro DESC

[10-Jul-2025 21:43:19 America/Santiago] Filtro ejecutivo: todos
[10-Jul-2025 21:43:19 America/Santiago] Filtro fecha inicio: 2025-01-01
[10-Jul-2025 21:43:19 America/Santiago] Filtro fecha fin: 2025-12-31
[10-Jul-2025 21:43:19 America/Santiago] INFO: Prospectos obtenidos: 5
[11-Jul-2025 01:43:20 UTC] === INTELETGROUP_ADMIN_DASHBOARD.PHP INICIADO ===
[10-Jul-2025 21:43:20 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 249
[10-Jul-2025 21:43:20 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 250
[10-Jul-2025 21:43:20 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 253
[10-Jul-2025 21:43:20 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 254
[10-Jul-2025 21:43:20 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 257
[10-Jul-2025 21:43:20 America/Santiago] DEBUG: Conexión a BD establecida correctamente
[10-Jul-2025 21:43:20 America/Santiago] DEBUG DASHBOARD: Usuario ID=4, Nombre BD='Oriana Camilo', Proyecto BD='inteletGroup'
[10-Jul-2025 21:43:20 America/Santiago] DEBUG DASHBOARD: Sesión nombre='Oriana Camilo', Sesión proyecto='inteletGroup'
[10-Jul-2025 21:43:20 America/Santiago] DEBUG: Obteniendo lista de ejecutivos...
[10-Jul-2025 21:43:20 America/Santiago] INFO: Filtros - Ejecutivo: todos, Periodo: año
[10-Jul-2025 21:43:20 America/Santiago] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[10-Jul-2025 21:43:20 America/Santiago] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[10-Jul-2025 21:43:20 America/Santiago] DEBUG: Ejecutando consulta de prospectos: 
    SELECT COUNT(*) as total_prospectos
    FROM tb_inteletgroup_prospectos p
    INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    WHERE u.proyecto = 'inteletGroup' AND DATE(p.fecha_registro) BETWEEN ? AND ?

[10-Jul-2025 21:43:20 America/Santiago] INFO: Total prospectos obtenido: 5
[10-Jul-2025 21:43:20 America/Santiago] INFO: Total documentos obtenido: 23
[10-Jul-2025 21:43:20 America/Santiago] INFO: Completitud promedio: 46.7
[10-Jul-2025 21:43:20 America/Santiago] DEBUG: Obteniendo prospectos por tipo de persona...
[10-Jul-2025 21:43:20 America/Santiago] DEBUG: Tipos encontrados: Array
(
    [Natural] => 3
    [Juridica] => 2
)

[10-Jul-2025 21:43:20 America/Santiago] INFO: Prospectos por ejecutivo: 4
[10-Jul-2025 21:43:20 America/Santiago] INFO: Datos de evolución obtenidos: 2
[10-Jul-2025 21:43:20 America/Santiago] INFO: Ejecutando consulta de prospectos
[10-Jul-2025 21:43:20 America/Santiago] Query: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.fecha_registro,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, p.telefono_celular, p.fecha_registro, u.nombre_usuario
    ORDER BY p.fecha_registro DESC

[10-Jul-2025 21:43:20 America/Santiago] Filtro ejecutivo: todos
[10-Jul-2025 21:43:20 America/Santiago] Filtro fecha inicio: 2025-01-01
[10-Jul-2025 21:43:20 America/Santiago] Filtro fecha fin: 2025-12-31
[10-Jul-2025 21:43:20 America/Santiago] INFO: Prospectos obtenidos: 5
[11-Jul-2025 01:43:22 UTC] === INTELETGROUP_ADMIN_DASHBOARD.PHP INICIADO ===
[10-Jul-2025 21:43:22 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 249
[10-Jul-2025 21:43:22 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 250
[10-Jul-2025 21:43:22 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 253
[10-Jul-2025 21:43:22 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 254
[10-Jul-2025 21:43:22 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 257
[10-Jul-2025 21:43:22 America/Santiago] DEBUG: Conexión a BD establecida correctamente
[10-Jul-2025 21:43:22 America/Santiago] DEBUG DASHBOARD: Usuario ID=4, Nombre BD='Oriana Camilo', Proyecto BD='inteletGroup'
[10-Jul-2025 21:43:22 America/Santiago] DEBUG DASHBOARD: Sesión nombre='Oriana Camilo', Sesión proyecto='inteletGroup'
[10-Jul-2025 21:43:22 America/Santiago] DEBUG: Obteniendo lista de ejecutivos...
[10-Jul-2025 21:43:22 America/Santiago] INFO: Filtros - Ejecutivo: todos, Periodo: año
[10-Jul-2025 21:43:22 America/Santiago] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[10-Jul-2025 21:43:22 America/Santiago] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[10-Jul-2025 21:43:22 America/Santiago] DEBUG: Ejecutando consulta de prospectos: 
    SELECT COUNT(*) as total_prospectos
    FROM tb_inteletgroup_prospectos p
    INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    WHERE u.proyecto = 'inteletGroup' AND DATE(p.fecha_registro) BETWEEN ? AND ?

[10-Jul-2025 21:43:22 America/Santiago] INFO: Total prospectos obtenido: 5
[10-Jul-2025 21:43:22 America/Santiago] INFO: Total documentos obtenido: 23
[10-Jul-2025 21:43:22 America/Santiago] INFO: Completitud promedio: 46.7
[10-Jul-2025 21:43:22 America/Santiago] DEBUG: Obteniendo prospectos por tipo de persona...
[10-Jul-2025 21:43:22 America/Santiago] DEBUG: Tipos encontrados: Array
(
    [Natural] => 3
    [Juridica] => 2
)

[10-Jul-2025 21:43:22 America/Santiago] INFO: Prospectos por ejecutivo: 4
[10-Jul-2025 21:43:22 America/Santiago] INFO: Datos de evolución obtenidos: 2
[10-Jul-2025 21:43:22 America/Santiago] INFO: Ejecutando consulta de prospectos
[10-Jul-2025 21:43:22 America/Santiago] Query: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.fecha_registro,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, p.telefono_celular, p.fecha_registro, u.nombre_usuario
    ORDER BY p.fecha_registro DESC

[10-Jul-2025 21:43:22 America/Santiago] Filtro ejecutivo: todos
[10-Jul-2025 21:43:22 America/Santiago] Filtro fecha inicio: 2025-01-01
[10-Jul-2025 21:43:22 America/Santiago] Filtro fecha fin: 2025-12-31
[10-Jul-2025 21:43:22 America/Santiago] INFO: Prospectos obtenidos: 5
[11-Jul-2025 01:43:24 UTC] === INTELETGROUP_ADMIN_DASHBOARD.PHP INICIADO ===
[10-Jul-2025 21:43:24 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 249
[10-Jul-2025 21:43:24 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 250
[10-Jul-2025 21:43:24 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 253
[10-Jul-2025 21:43:24 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 254
[10-Jul-2025 21:43:24 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 257
[10-Jul-2025 21:43:24 America/Santiago] DEBUG: Conexión a BD establecida correctamente
[10-Jul-2025 21:43:24 America/Santiago] DEBUG DASHBOARD: Usuario ID=4, Nombre BD='Oriana Camilo', Proyecto BD='inteletGroup'
[10-Jul-2025 21:43:24 America/Santiago] DEBUG DASHBOARD: Sesión nombre='Oriana Camilo', Sesión proyecto='inteletGroup'
[10-Jul-2025 21:43:24 America/Santiago] DEBUG: Obteniendo lista de ejecutivos...
[10-Jul-2025 21:43:24 America/Santiago] INFO: Filtros - Ejecutivo: todos, Periodo: año
[10-Jul-2025 21:43:24 America/Santiago] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[10-Jul-2025 21:43:24 America/Santiago] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[10-Jul-2025 21:43:24 America/Santiago] DEBUG: Ejecutando consulta de prospectos: 
    SELECT COUNT(*) as total_prospectos
    FROM tb_inteletgroup_prospectos p
    INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    WHERE u.proyecto = 'inteletGroup' AND DATE(p.fecha_registro) BETWEEN ? AND ?

[10-Jul-2025 21:43:24 America/Santiago] INFO: Total prospectos obtenido: 5
[10-Jul-2025 21:43:24 America/Santiago] INFO: Total documentos obtenido: 23
[10-Jul-2025 21:43:24 America/Santiago] INFO: Completitud promedio: 46.7
[10-Jul-2025 21:43:24 America/Santiago] DEBUG: Obteniendo prospectos por tipo de persona...
[10-Jul-2025 21:43:24 America/Santiago] DEBUG: Tipos encontrados: Array
(
    [Natural] => 3
    [Juridica] => 2
)

[10-Jul-2025 21:43:24 America/Santiago] INFO: Prospectos por ejecutivo: 4
[10-Jul-2025 21:43:24 America/Santiago] INFO: Datos de evolución obtenidos: 2
[10-Jul-2025 21:43:24 America/Santiago] INFO: Ejecutando consulta de prospectos
[10-Jul-2025 21:43:24 America/Santiago] Query: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.fecha_registro,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, p.telefono_celular, p.fecha_registro, u.nombre_usuario
    ORDER BY p.fecha_registro DESC

[10-Jul-2025 21:43:24 America/Santiago] Filtro ejecutivo: todos
[10-Jul-2025 21:43:24 America/Santiago] Filtro fecha inicio: 2025-01-01
[10-Jul-2025 21:43:24 America/Santiago] Filtro fecha fin: 2025-12-31
[10-Jul-2025 21:43:24 America/Santiago] INFO: Prospectos obtenidos: 5
[11-Jul-2025 01:43:25 UTC] === INTELETGROUP_ADMIN_DASHBOARD.PHP INICIADO ===
[10-Jul-2025 21:43:25 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 249
[10-Jul-2025 21:43:25 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 250
[10-Jul-2025 21:43:25 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 253
[10-Jul-2025 21:43:25 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 254
[10-Jul-2025 21:43:25 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 257
[10-Jul-2025 21:43:25 America/Santiago] DEBUG: Conexión a BD establecida correctamente
[10-Jul-2025 21:43:25 America/Santiago] DEBUG DASHBOARD: Usuario ID=4, Nombre BD='Oriana Camilo', Proyecto BD='inteletGroup'
[10-Jul-2025 21:43:25 America/Santiago] DEBUG DASHBOARD: Sesión nombre='Oriana Camilo', Sesión proyecto='inteletGroup'
[10-Jul-2025 21:43:25 America/Santiago] DEBUG: Obteniendo lista de ejecutivos...
[10-Jul-2025 21:43:25 America/Santiago] INFO: Filtros - Ejecutivo: todos, Periodo: año
[10-Jul-2025 21:43:25 America/Santiago] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[10-Jul-2025 21:43:25 America/Santiago] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[10-Jul-2025 21:43:25 America/Santiago] DEBUG: Ejecutando consulta de prospectos: 
    SELECT COUNT(*) as total_prospectos
    FROM tb_inteletgroup_prospectos p
    INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    WHERE u.proyecto = 'inteletGroup' AND DATE(p.fecha_registro) BETWEEN ? AND ?

[10-Jul-2025 21:43:25 America/Santiago] INFO: Total prospectos obtenido: 5
[10-Jul-2025 21:43:25 America/Santiago] INFO: Total documentos obtenido: 23
[10-Jul-2025 21:43:25 America/Santiago] INFO: Completitud promedio: 46.7
[10-Jul-2025 21:43:25 America/Santiago] DEBUG: Obteniendo prospectos por tipo de persona...
[10-Jul-2025 21:43:25 America/Santiago] DEBUG: Tipos encontrados: Array
(
    [Natural] => 3
    [Juridica] => 2
)

[10-Jul-2025 21:43:25 America/Santiago] INFO: Prospectos por ejecutivo: 4
[10-Jul-2025 21:43:25 America/Santiago] INFO: Datos de evolución obtenidos: 2
[10-Jul-2025 21:43:25 America/Santiago] INFO: Ejecutando consulta de prospectos
[10-Jul-2025 21:43:25 America/Santiago] Query: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.fecha_registro,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, p.telefono_celular, p.fecha_registro, u.nombre_usuario
    ORDER BY p.fecha_registro DESC

[10-Jul-2025 21:43:25 America/Santiago] Filtro ejecutivo: todos
[10-Jul-2025 21:43:25 America/Santiago] Filtro fecha inicio: 2025-01-01
[10-Jul-2025 21:43:25 America/Santiago] Filtro fecha fin: 2025-12-31
[10-Jul-2025 21:43:25 America/Santiago] INFO: Prospectos obtenidos: 5
[11-Jul-2025 01:43:28 UTC] === INTELETGROUP_ADMIN_DASHBOARD.PHP INICIADO ===
[10-Jul-2025 21:43:28 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 249
[10-Jul-2025 21:43:28 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 250
[10-Jul-2025 21:43:28 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 253
[10-Jul-2025 21:43:28 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 254
[10-Jul-2025 21:43:28 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 257
[10-Jul-2025 21:43:28 America/Santiago] DEBUG: Conexión a BD establecida correctamente
[10-Jul-2025 21:43:28 America/Santiago] DEBUG DASHBOARD: Usuario ID=4, Nombre BD='Oriana Camilo', Proyecto BD='inteletGroup'
[10-Jul-2025 21:43:28 America/Santiago] DEBUG DASHBOARD: Sesión nombre='Oriana Camilo', Sesión proyecto='inteletGroup'
[10-Jul-2025 21:43:28 America/Santiago] DEBUG: Obteniendo lista de ejecutivos...
[10-Jul-2025 21:43:28 America/Santiago] INFO: Filtros - Ejecutivo: todos, Periodo: año
[10-Jul-2025 21:43:28 America/Santiago] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[10-Jul-2025 21:43:28 America/Santiago] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[10-Jul-2025 21:43:28 America/Santiago] DEBUG: Ejecutando consulta de prospectos: 
    SELECT COUNT(*) as total_prospectos
    FROM tb_inteletgroup_prospectos p
    INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    WHERE u.proyecto = 'inteletGroup' AND DATE(p.fecha_registro) BETWEEN ? AND ?

[10-Jul-2025 21:43:28 America/Santiago] INFO: Total prospectos obtenido: 5
[10-Jul-2025 21:43:28 America/Santiago] INFO: Total documentos obtenido: 23
[10-Jul-2025 21:43:28 America/Santiago] INFO: Completitud promedio: 46.7
[10-Jul-2025 21:43:28 America/Santiago] DEBUG: Obteniendo prospectos por tipo de persona...
[10-Jul-2025 21:43:28 America/Santiago] DEBUG: Tipos encontrados: Array
(
    [Natural] => 3
    [Juridica] => 2
)

[10-Jul-2025 21:43:28 America/Santiago] INFO: Prospectos por ejecutivo: 4
[10-Jul-2025 21:43:28 America/Santiago] INFO: Datos de evolución obtenidos: 2
[10-Jul-2025 21:43:28 America/Santiago] INFO: Ejecutando consulta de prospectos
[10-Jul-2025 21:43:28 America/Santiago] Query: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.fecha_registro,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, p.telefono_celular, p.fecha_registro, u.nombre_usuario
    ORDER BY p.fecha_registro DESC

[10-Jul-2025 21:43:28 America/Santiago] Filtro ejecutivo: todos
[10-Jul-2025 21:43:28 America/Santiago] Filtro fecha inicio: 2025-01-01
[10-Jul-2025 21:43:28 America/Santiago] Filtro fecha fin: 2025-12-31
[10-Jul-2025 21:43:28 America/Santiago] INFO: Prospectos obtenidos: 5
[11-Jul-2025 01:43:30 UTC] === INTELETGROUP_ADMIN_DASHBOARD.PHP INICIADO ===
[10-Jul-2025 21:43:30 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 249
[10-Jul-2025 21:43:30 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 250
[10-Jul-2025 21:43:30 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 253
[10-Jul-2025 21:43:30 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 254
[10-Jul-2025 21:43:30 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 257
[10-Jul-2025 21:43:30 America/Santiago] DEBUG: Conexión a BD establecida correctamente
[10-Jul-2025 21:43:30 America/Santiago] DEBUG DASHBOARD: Usuario ID=4, Nombre BD='Oriana Camilo', Proyecto BD='inteletGroup'
[10-Jul-2025 21:43:30 America/Santiago] DEBUG DASHBOARD: Sesión nombre='Oriana Camilo', Sesión proyecto='inteletGroup'
[10-Jul-2025 21:43:30 America/Santiago] DEBUG: Obteniendo lista de ejecutivos...
[10-Jul-2025 21:43:30 America/Santiago] INFO: Filtros - Ejecutivo: todos, Periodo: año
[10-Jul-2025 21:43:30 America/Santiago] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[10-Jul-2025 21:43:30 America/Santiago] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[10-Jul-2025 21:43:30 America/Santiago] DEBUG: Ejecutando consulta de prospectos: 
    SELECT COUNT(*) as total_prospectos
    FROM tb_inteletgroup_prospectos p
    INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    WHERE u.proyecto = 'inteletGroup' AND DATE(p.fecha_registro) BETWEEN ? AND ?

[10-Jul-2025 21:43:30 America/Santiago] INFO: Total prospectos obtenido: 5
[10-Jul-2025 21:43:30 America/Santiago] INFO: Total documentos obtenido: 23
[10-Jul-2025 21:43:30 America/Santiago] INFO: Completitud promedio: 46.7
[10-Jul-2025 21:43:30 America/Santiago] DEBUG: Obteniendo prospectos por tipo de persona...
[10-Jul-2025 21:43:30 America/Santiago] DEBUG: Tipos encontrados: Array
(
    [Natural] => 3
    [Juridica] => 2
)

[10-Jul-2025 21:43:30 America/Santiago] INFO: Prospectos por ejecutivo: 4
[10-Jul-2025 21:43:30 America/Santiago] INFO: Datos de evolución obtenidos: 2
[10-Jul-2025 21:43:30 America/Santiago] INFO: Ejecutando consulta de prospectos
[10-Jul-2025 21:43:30 America/Santiago] Query: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.fecha_registro,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, p.telefono_celular, p.fecha_registro, u.nombre_usuario
    ORDER BY p.fecha_registro DESC

[10-Jul-2025 21:43:30 America/Santiago] Filtro ejecutivo: todos
[10-Jul-2025 21:43:30 America/Santiago] Filtro fecha inicio: 2025-01-01
[10-Jul-2025 21:43:30 America/Santiago] Filtro fecha fin: 2025-12-31
[10-Jul-2025 21:43:30 America/Santiago] INFO: Prospectos obtenidos: 5
[11-Jul-2025 01:43:32 UTC] === INTELETGROUP_ADMIN_DASHBOARD.PHP INICIADO ===
[10-Jul-2025 21:43:32 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 249
[10-Jul-2025 21:43:32 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 250
[10-Jul-2025 21:43:32 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 253
[10-Jul-2025 21:43:32 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 254
[10-Jul-2025 21:43:32 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 257
[10-Jul-2025 21:43:32 America/Santiago] DEBUG: Conexión a BD establecida correctamente
[10-Jul-2025 21:43:32 America/Santiago] DEBUG DASHBOARD: Usuario ID=4, Nombre BD='Oriana Camilo', Proyecto BD='inteletGroup'
[10-Jul-2025 21:43:32 America/Santiago] DEBUG DASHBOARD: Sesión nombre='Oriana Camilo', Sesión proyecto='inteletGroup'
[10-Jul-2025 21:43:32 America/Santiago] DEBUG: Obteniendo lista de ejecutivos...
[10-Jul-2025 21:43:32 America/Santiago] INFO: Filtros - Ejecutivo: todos, Periodo: año
[10-Jul-2025 21:43:32 America/Santiago] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[10-Jul-2025 21:43:32 America/Santiago] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[10-Jul-2025 21:43:32 America/Santiago] DEBUG: Ejecutando consulta de prospectos: 
    SELECT COUNT(*) as total_prospectos
    FROM tb_inteletgroup_prospectos p
    INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    WHERE u.proyecto = 'inteletGroup' AND DATE(p.fecha_registro) BETWEEN ? AND ?

[10-Jul-2025 21:43:32 America/Santiago] INFO: Total prospectos obtenido: 5
[10-Jul-2025 21:43:32 America/Santiago] INFO: Total documentos obtenido: 23
[10-Jul-2025 21:43:32 America/Santiago] INFO: Completitud promedio: 46.7
[10-Jul-2025 21:43:32 America/Santiago] DEBUG: Obteniendo prospectos por tipo de persona...
[10-Jul-2025 21:43:32 America/Santiago] DEBUG: Tipos encontrados: Array
(
    [Natural] => 3
    [Juridica] => 2
)

[10-Jul-2025 21:43:32 America/Santiago] INFO: Prospectos por ejecutivo: 4
[10-Jul-2025 21:43:32 America/Santiago] INFO: Datos de evolución obtenidos: 2
[10-Jul-2025 21:43:32 America/Santiago] INFO: Ejecutando consulta de prospectos
[10-Jul-2025 21:43:32 America/Santiago] Query: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.fecha_registro,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, p.telefono_celular, p.fecha_registro, u.nombre_usuario
    ORDER BY p.fecha_registro DESC

[10-Jul-2025 21:43:32 America/Santiago] Filtro ejecutivo: todos
[10-Jul-2025 21:43:32 America/Santiago] Filtro fecha inicio: 2025-01-01
[10-Jul-2025 21:43:32 America/Santiago] Filtro fecha fin: 2025-12-31
[10-Jul-2025 21:43:32 America/Santiago] INFO: Prospectos obtenidos: 5
[11-Jul-2025 01:43:34 UTC] === INTELETGROUP_ADMIN_DASHBOARD.PHP INICIADO ===
[10-Jul-2025 21:43:34 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 249
[10-Jul-2025 21:43:34 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 250
[10-Jul-2025 21:43:34 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 253
[10-Jul-2025 21:43:34 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 254
[10-Jul-2025 21:43:34 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 257
[10-Jul-2025 21:43:34 America/Santiago] DEBUG: Conexión a BD establecida correctamente
[10-Jul-2025 21:43:34 America/Santiago] DEBUG DASHBOARD: Usuario ID=4, Nombre BD='Oriana Camilo', Proyecto BD='inteletGroup'
[10-Jul-2025 21:43:34 America/Santiago] DEBUG DASHBOARD: Sesión nombre='Oriana Camilo', Sesión proyecto='inteletGroup'
[10-Jul-2025 21:43:34 America/Santiago] DEBUG: Obteniendo lista de ejecutivos...
[10-Jul-2025 21:43:34 America/Santiago] INFO: Filtros - Ejecutivo: todos, Periodo: año
[10-Jul-2025 21:43:34 America/Santiago] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[10-Jul-2025 21:43:34 America/Santiago] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[10-Jul-2025 21:43:34 America/Santiago] DEBUG: Ejecutando consulta de prospectos: 
    SELECT COUNT(*) as total_prospectos
    FROM tb_inteletgroup_prospectos p
    INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    WHERE u.proyecto = 'inteletGroup' AND DATE(p.fecha_registro) BETWEEN ? AND ?

[10-Jul-2025 21:43:34 America/Santiago] INFO: Total prospectos obtenido: 5
[10-Jul-2025 21:43:34 America/Santiago] INFO: Total documentos obtenido: 23
[10-Jul-2025 21:43:34 America/Santiago] INFO: Completitud promedio: 46.7
[10-Jul-2025 21:43:34 America/Santiago] DEBUG: Obteniendo prospectos por tipo de persona...
[10-Jul-2025 21:43:34 America/Santiago] DEBUG: Tipos encontrados: Array
(
    [Natural] => 3
    [Juridica] => 2
)

[10-Jul-2025 21:43:34 America/Santiago] INFO: Prospectos por ejecutivo: 4
[10-Jul-2025 21:43:34 America/Santiago] INFO: Datos de evolución obtenidos: 2
[10-Jul-2025 21:43:34 America/Santiago] INFO: Ejecutando consulta de prospectos
[10-Jul-2025 21:43:34 America/Santiago] Query: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.fecha_registro,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, p.telefono_celular, p.fecha_registro, u.nombre_usuario
    ORDER BY p.fecha_registro DESC

[10-Jul-2025 21:43:34 America/Santiago] Filtro ejecutivo: todos
[10-Jul-2025 21:43:34 America/Santiago] Filtro fecha inicio: 2025-01-01
[10-Jul-2025 21:43:34 America/Santiago] Filtro fecha fin: 2025-12-31
[10-Jul-2025 21:43:34 America/Santiago] INFO: Prospectos obtenidos: 5
[11-Jul-2025 01:43:36 UTC] === INTELETGROUP_ADMIN_DASHBOARD.PHP INICIADO ===
[10-Jul-2025 21:43:36 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 249
[10-Jul-2025 21:43:36 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 250
[10-Jul-2025 21:43:36 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 253
[10-Jul-2025 21:43:36 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 254
[10-Jul-2025 21:43:36 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 257
[10-Jul-2025 21:43:36 America/Santiago] DEBUG: Conexión a BD establecida correctamente
[10-Jul-2025 21:43:36 America/Santiago] DEBUG DASHBOARD: Usuario ID=4, Nombre BD='Oriana Camilo', Proyecto BD='inteletGroup'
[10-Jul-2025 21:43:36 America/Santiago] DEBUG DASHBOARD: Sesión nombre='Oriana Camilo', Sesión proyecto='inteletGroup'
[10-Jul-2025 21:43:36 America/Santiago] DEBUG: Obteniendo lista de ejecutivos...
[10-Jul-2025 21:43:36 America/Santiago] INFO: Filtros - Ejecutivo: todos, Periodo: año
[10-Jul-2025 21:43:36 America/Santiago] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[10-Jul-2025 21:43:36 America/Santiago] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[10-Jul-2025 21:43:36 America/Santiago] DEBUG: Ejecutando consulta de prospectos: 
    SELECT COUNT(*) as total_prospectos
    FROM tb_inteletgroup_prospectos p
    INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    WHERE u.proyecto = 'inteletGroup' AND DATE(p.fecha_registro) BETWEEN ? AND ?

[10-Jul-2025 21:43:36 America/Santiago] INFO: Total prospectos obtenido: 5
[10-Jul-2025 21:43:36 America/Santiago] INFO: Total documentos obtenido: 23
[10-Jul-2025 21:43:36 America/Santiago] INFO: Completitud promedio: 46.7
[10-Jul-2025 21:43:36 America/Santiago] DEBUG: Obteniendo prospectos por tipo de persona...
[10-Jul-2025 21:43:36 America/Santiago] DEBUG: Tipos encontrados: Array
(
    [Natural] => 3
    [Juridica] => 2
)

[10-Jul-2025 21:43:36 America/Santiago] INFO: Prospectos por ejecutivo: 4
[10-Jul-2025 21:43:36 America/Santiago] INFO: Datos de evolución obtenidos: 2
[10-Jul-2025 21:43:36 America/Santiago] INFO: Ejecutando consulta de prospectos
[10-Jul-2025 21:43:36 America/Santiago] Query: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.fecha_registro,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, p.telefono_celular, p.fecha_registro, u.nombre_usuario
    ORDER BY p.fecha_registro DESC

[10-Jul-2025 21:43:36 America/Santiago] Filtro ejecutivo: todos
[10-Jul-2025 21:43:36 America/Santiago] Filtro fecha inicio: 2025-01-01
[10-Jul-2025 21:43:36 America/Santiago] Filtro fecha fin: 2025-12-31
[10-Jul-2025 21:43:36 America/Santiago] INFO: Prospectos obtenidos: 5
[11-Jul-2025 01:43:37 UTC] === INTELETGROUP_ADMIN_DASHBOARD.PHP INICIADO ===
[10-Jul-2025 21:43:37 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 249
[10-Jul-2025 21:43:37 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 250
[10-Jul-2025 21:43:37 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 253
[10-Jul-2025 21:43:37 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 254
[10-Jul-2025 21:43:37 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 257
[10-Jul-2025 21:43:37 America/Santiago] DEBUG: Conexión a BD establecida correctamente
[10-Jul-2025 21:43:37 America/Santiago] DEBUG DASHBOARD: Usuario ID=4, Nombre BD='Oriana Camilo', Proyecto BD='inteletGroup'
[10-Jul-2025 21:43:37 America/Santiago] DEBUG DASHBOARD: Sesión nombre='Oriana Camilo', Sesión proyecto='inteletGroup'
[10-Jul-2025 21:43:37 America/Santiago] DEBUG: Obteniendo lista de ejecutivos...
[10-Jul-2025 21:43:37 America/Santiago] INFO: Filtros - Ejecutivo: todos, Periodo: año
[10-Jul-2025 21:43:37 America/Santiago] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[10-Jul-2025 21:43:37 America/Santiago] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[10-Jul-2025 21:43:37 America/Santiago] DEBUG: Ejecutando consulta de prospectos: 
    SELECT COUNT(*) as total_prospectos
    FROM tb_inteletgroup_prospectos p
    INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    WHERE u.proyecto = 'inteletGroup' AND DATE(p.fecha_registro) BETWEEN ? AND ?

[10-Jul-2025 21:43:37 America/Santiago] INFO: Total prospectos obtenido: 5
[10-Jul-2025 21:43:37 America/Santiago] INFO: Total documentos obtenido: 23
[10-Jul-2025 21:43:37 America/Santiago] INFO: Completitud promedio: 46.7
[10-Jul-2025 21:43:37 America/Santiago] DEBUG: Obteniendo prospectos por tipo de persona...
[10-Jul-2025 21:43:37 America/Santiago] DEBUG: Tipos encontrados: Array
(
    [Natural] => 3
    [Juridica] => 2
)

[10-Jul-2025 21:43:37 America/Santiago] INFO: Prospectos por ejecutivo: 4
[10-Jul-2025 21:43:37 America/Santiago] INFO: Datos de evolución obtenidos: 2
[10-Jul-2025 21:43:37 America/Santiago] INFO: Ejecutando consulta de prospectos
[10-Jul-2025 21:43:37 America/Santiago] Query: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.fecha_registro,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, p.telefono_celular, p.fecha_registro, u.nombre_usuario
    ORDER BY p.fecha_registro DESC

[10-Jul-2025 21:43:37 America/Santiago] Filtro ejecutivo: todos
[10-Jul-2025 21:43:37 America/Santiago] Filtro fecha inicio: 2025-01-01
[10-Jul-2025 21:43:37 America/Santiago] Filtro fecha fin: 2025-12-31
[10-Jul-2025 21:43:37 America/Santiago] INFO: Prospectos obtenidos: 5
[11-Jul-2025 01:43:39 UTC] === INTELETGROUP_ADMIN_DASHBOARD.PHP INICIADO ===
[10-Jul-2025 21:43:39 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 249
[10-Jul-2025 21:43:39 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 250
[10-Jul-2025 21:43:39 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 253
[10-Jul-2025 21:43:39 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 254
[10-Jul-2025 21:43:39 America/Santiago] PHP Warning:  ini_set(): A session is active. You cannot change the session module's ini settings at this time in /home/<USER>/public_html/intranet/dist/config.php on line 257
[10-Jul-2025 21:43:39 America/Santiago] DEBUG: Conexión a BD establecida correctamente
[10-Jul-2025 21:43:39 America/Santiago] DEBUG DASHBOARD: Usuario ID=4, Nombre BD='Oriana Camilo', Proyecto BD='inteletGroup'
[10-Jul-2025 21:43:39 America/Santiago] DEBUG DASHBOARD: Sesión nombre='Oriana Camilo', Sesión proyecto='inteletGroup'
[10-Jul-2025 21:43:39 America/Santiago] DEBUG: Obteniendo lista de ejecutivos...
[10-Jul-2025 21:43:39 America/Santiago] INFO: Filtros - Ejecutivo: todos, Periodo: año
[10-Jul-2025 21:43:39 America/Santiago] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[10-Jul-2025 21:43:39 America/Santiago] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[10-Jul-2025 21:43:39 America/Santiago] DEBUG: Ejecutando consulta de prospectos: 
    SELECT COUNT(*) as total_prospectos
    FROM tb_inteletgroup_prospectos p
    INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    WHERE u.proyecto = 'inteletGroup' AND DATE(p.fecha_registro) BETWEEN ? AND ?

[10-Jul-2025 21:43:39 America/Santiago] INFO: Total prospectos obtenido: 5
[10-Jul-2025 21:43:39 America/Santiago] INFO: Total documentos obtenido: 23
[10-Jul-2025 21:43:39 America/Santiago] INFO: Completitud promedio: 46.7
[10-Jul-2025 21:43:39 America/Santiago] DEBUG: Obteniendo prospectos por tipo de persona...
[10-Jul-2025 21:43:39 America/Santiago] DEBUG: Tipos encontrados: Array
(
    [Natural] => 3
    [Juridica] => 2
)

[10-Jul-2025 21:43:39 America/Santiago] INFO: Prospectos por ejecutivo: 4
[10-Jul-2025 21:43:39 America/Santiago] INFO: Datos de evolución obtenidos: 2
[10-Jul-2025 21:43:39 America/Santiago] INFO: Ejecutando consulta de prospectos
[10-Jul-2025 21:43:39 America/Santiago] Query: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.fecha_registro,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, p.telefono_celular, p.fecha_registro, u.nombre_usuario
    ORDER BY p.fecha_registro DESC

[10-Jul-2025 21:43:39 America/Santiago] Filtro ejecutivo: todos
[10-Jul-2025 21:43:39 America/Santiago] Filtro fecha inicio: 2025-01-01
[10-Jul-2025 21:43:39 America/Santiago] Filtro fecha fin: 2025-12-31
[10-Jul-2025 21:43:39 America/Santiago] INFO: Prospectos obtenidos: 5