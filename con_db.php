<?php
/**
 * Conexión simplificada a la base de datos con logging mejorado
 * Compatible con desarrollo local y hosting
 */

// Configurar reporte de errores para depuración
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Variables globales de estado
$db_connection_ok = false;
$db_error_message = '';
$db_config_used = '';

// Crear carpeta de logs si no existe
$log_dir = dirname(__FILE__) . '/logs';
if (!is_dir($log_dir)) {
    @mkdir($log_dir, 0755, true);
}

// Función de logging simplificada y mejorada
function escribir_log($mensaje, $tipo = 'info') {
    global $log_dir;
    
    $fecha = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'CLI';
    $script = basename($_SERVER['SCRIPT_FILENAME'] ?? 'Unknown');
    
    // Formato del mensaje
    $mensaje_log = "[$fecha] [$tipo] [$ip] [$script] $mensaje" . PHP_EOL;
    
    // Solo mostrar en consola HTML si NO es un endpoint AJAX o JSON
    $is_ajax_endpoint = (
        strpos($_SERVER['REQUEST_URI'] ?? '', 'Controller') !== false ||
        strpos($_SERVER['REQUEST_URI'] ?? '', 'ajax') !== false ||
        strpos($_SERVER['REQUEST_URI'] ?? '', 'api/') !== false ||
        (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false) ||
        (isset($_SERVER['CONTENT_TYPE']) && strpos($_SERVER['CONTENT_TYPE'], 'application/json') !== false)
    );
    
    if (!$is_ajax_endpoint && ($tipo === 'info' || $tipo === 'error')) {
        echo "<!-- DB LOG: $mensaje -->\n";
    }
    
    // Guardar en archivo
    $log_file = $log_dir . '/db_connection.log';
    @file_put_contents($log_file, $mensaje_log, FILE_APPEND | LOCK_EX);
    
    // Log adicional para errores
    if ($tipo === 'error') {
        error_log("DB ERROR: $mensaje");
    }
}

// Función para detectar el ambiente actual
function detectar_ambiente() {
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    
    // Es desarrollo local?
    if (strpos($host, 'localhost') !== false || 
        strpos($host, '127.0.0.1') !== false ||
        strpos($host, '::1') !== false ||
        strpos($_SERVER['DOCUMENT_ROOT'] ?? '', 'xampp') !== false) {
        return 'local';
    }
    
    return 'hosting';
}

$ambiente = detectar_ambiente();
escribir_log("=== INICIO DE CONEXIÓN ===", 'info');
escribir_log("Ambiente detectado: $ambiente", 'info');
escribir_log("Host: " . ($_SERVER['HTTP_HOST'] ?? 'Unknown'), 'info');
escribir_log("Document Root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown'), 'info');

// Configuraciones de conexión por ambiente
$db_configs = [];

if ($ambiente === 'local') {
    // Configuraciones para desarrollo local - conectar remotamente al hosting
    $db_configs = [
        'remote_hosting_domain' => [
            'host' => 'gestarservicios.cl',
            'port' => 3306,
            'database' => 'gestarse_ncornejo7_experian',
            'username' => 'gestarse_ncornejo7_experian',
            'password' => 'N1c0l7as17'
        ],
        'remote_hosting_ip' => [
            'host' => '*************',
            'port' => 3306,
            'database' => 'gestarse_ncornejo7_experian',
            'username' => 'gestarse_ncornejo7_experian',
            'password' => 'N1c0l7as17'
        ],
        'local_tcp' => [
            'host' => '127.0.0.1',
            'port' => 3306,
            'database' => 'gestarse_experian',
            'username' => 'root',
            'password' => ''
        ],
        'local_xampp' => [
            'host' => 'localhost',
            'port' => 3306,
            'database' => 'gestarse_experian',
            'username' => 'root',
            'password' => ''
        ]
    ];
} else {
    // Configuración para producción/hosting
    $db_configs = [
        'hosting' => [
            'host' => 'localhost',
            'port' => 3306,
            'database' => 'gestarse_ncornejo7_experian',
            'username' => 'gestarse_ncornejo7_experian',
            'password' => 'N1c0l7as17'
        ]
    ];
}

// Variables globales de conexión
$mysqli = null;
$conn = null;
$conexion = null;

// Probar cada configuración
foreach ($db_configs as $config_name => $config) {
    escribir_log("Probando configuración: $config_name", 'info');
    escribir_log("  Host: {$config['host']}:{$config['port']}", 'info');
    escribir_log("  Database: {$config['database']}", 'info');
    escribir_log("  Username: {$config['username']}", 'info');
    
    try {
        // Configurar mysqli para que no lance excepciones
        mysqli_report(MYSQLI_REPORT_OFF);
        
        // Crear conexión con timeout
        $test_conn = new mysqli();
        
        // Configurar opciones ANTES de conectar - timeout más agresivo
        $test_conn->options(MYSQLI_OPT_CONNECT_TIMEOUT, 1); // Reducir a 1 segundo
        
        // Intentar conectar
        $result = @$test_conn->real_connect(
            $config['host'],
            $config['username'],
            $config['password'],
            $config['database'],
            $config['port']
        );
        
        if (!$result || $test_conn->connect_error) {
            $error = $test_conn->connect_error ?: 'Error desconocido';
            escribir_log("  ❌ Error: $error", 'error');
            $db_error_message = $error;
            continue;
        }
        
        // Verificar que la conexión esté activa
        if (!@$test_conn->ping()) {
            escribir_log("  ❌ Error: La conexión no responde al ping", 'error');
            continue;
        }
        
        // Conexión exitosa
        escribir_log("  ✅ CONEXIÓN EXITOSA!", 'info');
        $mysqli = $test_conn;
        $conn = $test_conn;
        $conexion = $test_conn;
        $db_connection_ok = true;
        $db_config_used = $config_name;
        break;
        
    } catch (Exception $e) {
        escribir_log("  ❌ Excepción: " . $e->getMessage(), 'error');
        $db_error_message = $e->getMessage();
    } catch (Throwable $t) {
        escribir_log("  ❌ Error grave: " . $t->getMessage(), 'error');
        $db_error_message = $t->getMessage();
    }
}

// Si no se pudo conectar, crear objeto dummy
if (!$db_connection_ok) {
    escribir_log("NO SE PUDO ESTABLECER CONEXIÓN CON NINGUNA CONFIGURACIÓN", 'error');
    
    // Crear clase dummy para evitar errores fatales
    class DummyMysqli {
        public $connect_error = "No hay conexión a la base de datos";
        public $error = "No hay conexión";
        public $errno = 0;
        public $connect_errno = 2002;
        
        public function query($sql) { 
            return false; 
        }
        
        public function prepare($sql) { 
            return false; 
        }
        
        public function real_escape_string($str) { 
            return addslashes($str); 
        }
        
        public function close() { 
            return true; 
        }
        
        public function ping() {
            return false;
        }
    }
    
    $mysqli = new DummyMysqli();
    $conn = $mysqli;
    $conexion = $mysqli;
    
    // Si no hay mensaje de error específico
    if (empty($db_error_message)) {
        $db_error_message = "No se pudo conectar a ninguna base de datos configurada";
    }
}

// Función para crear conexión PDO
function createPDOConnection() {
    global $db_connection_ok, $db_configs, $db_config_used;
    
    if (!$db_connection_ok || !isset($db_configs[$db_config_used])) {
        escribir_log("No se puede crear conexión PDO: no hay conexión mysqli activa", 'error');
        return null;
    }
    
    $config = $db_configs[$db_config_used];
    
    try {
        $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset=utf8";
        $pdo = new PDO(
            $dsn,
            $config['username'],
            $config['password'],
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_TIMEOUT => 3
            ]
        );
        escribir_log("Conexión PDO creada exitosamente", 'info');
        return $pdo;
    } catch (PDOException $e) {
        escribir_log("Error creando conexión PDO: " . $e->getMessage(), 'error');
        return null;
    }
}

// Alias para compatibilidad
if (!function_exists('getDBConnection')) {
    function getDBConnection() {
        return createPDOConnection();
    }
}

// Función para cerrar la conexión
function cerrarConexion() {
    global $mysqli, $conn, $conexion;
    
    if ($mysqli instanceof mysqli) {
        try {
            if (@$mysqli->ping()) {
                $mysqli->close();
                escribir_log("Conexión cerrada correctamente", 'info');
            }
        } catch (Throwable $e) {
            // Ignorar errores al cerrar
        }
    }
    
    $mysqli = null;
    $conn = null;
    $conexion = null;
}

// Registrar función de cierre
register_shutdown_function('cerrarConexion');

// Mostrar resumen
escribir_log("=== RESUMEN DE CONEXIÓN ===", 'info');
escribir_log("Estado: " . ($db_connection_ok ? "CONECTADO" : "DESCONECTADO"), 'info');
escribir_log("Configuración usada: " . ($db_config_used ?: "Ninguna"), 'info');
escribir_log("Error: " . ($db_error_message ?: "Ninguno"), 'info');
escribir_log("========================================", 'info');

// Solo mostrar información si se ejecuta directamente
if (basename($_SERVER['SCRIPT_FILENAME']) == basename(__FILE__)) {
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Estado de Conexión DB</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .success { color: green; }
            .error { color: red; }
            .info { color: blue; }
            pre { background: #f4f4f4; padding: 10px; border-radius: 5px; }
            table { border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
        </style>
    </head>
    <body>
        <h1>Estado de Conexión a Base de Datos</h1>
        
        <h2>Resumen</h2>
        <p>Ambiente: <strong><?php echo $ambiente; ?></strong></p>
        <p>Estado: <strong class="<?php echo $db_connection_ok ? 'success' : 'error'; ?>">
            <?php echo $db_connection_ok ? '✅ CONECTADO' : '❌ DESCONECTADO'; ?>
        </strong></p>
        <p>Configuración usada: <strong><?php echo $db_config_used ?: 'Ninguna'; ?></strong></p>
        <?php if (!$db_connection_ok): ?>
        <p>Error: <strong class="error"><?php echo htmlspecialchars($db_error_message); ?></strong></p>
        <?php endif; ?>
        
        <h2>Configuraciones Disponibles</h2>
        <table>
            <tr>
                <th>Nombre</th>
                <th>Host</th>
                <th>Puerto</th>
                <th>Base de datos</th>
                <th>Usuario</th>
            </tr>
            <?php foreach ($db_configs as $name => $config): ?>
            <tr>
                <td><?php echo $name; ?></td>
                <td><?php echo $config['host']; ?></td>
                <td><?php echo $config['port']; ?></td>
                <td><?php echo $config['database']; ?></td>
                <td><?php echo $config['username']; ?></td>
            </tr>
            <?php endforeach; ?>
        </table>
        
        <h2>Información del Sistema</h2>
        <pre>
PHP Version: <?php echo phpversion(); ?>
Server: <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?>
OS: <?php echo PHP_OS; ?>
        </pre>
        
        <h2>Logs Recientes</h2>
        <pre><?php 
        $log_file = $log_dir . '/db_connection.log';
        if (file_exists($log_file)) {
            $lines = file($log_file);
            $recent_lines = array_slice($lines, -20);
            echo htmlspecialchars(implode('', $recent_lines));
        } else {
            echo "No hay logs disponibles";
        }
        ?></pre>
    </body>
    </html>
    <?php
}
?>