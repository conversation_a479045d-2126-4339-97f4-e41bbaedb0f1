/**
 * Script de anti-caché SUPER SIMPLIFICADO
 * NO FORZAR RECARGAS PARA EVITAR BUCLES INFINITOS
 */

(function() {
    'use strict';

    console.log('🚫 INICIANDO LIMPIEZA TOTAL DE CACHÉ');

    // Solo limpiar localStorage/sessionStorage una vez
    try {
        // Limpiar solo claves específicas, no todo
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && (key.includes('cache') || key.includes('version'))) {
                keysToRemove.push(key);
            }
        }
        keysToRemove.forEach(key => localStorage.removeItem(key));
        
        console.log('✅ localStorage y sessionStorage limpiados');
    } catch(e) {
        console.warn('⚠️ Error limpiando storage:', e);
    }

    // Cache Storage - limpiar si existe
    try {
        if ('caches' in window) {
            caches.keys().then(cacheNames => {
                if (cacheNames.length > 0) {
                    return Promise.all(
                        cacheNames.map(cacheName => {
                            return caches.delete(cacheName);
                        })
                    );
                }
            }).then(() => {
                console.log('✅ Cache Storage limpiado');
            }).catch(e => console.warn('⚠️ Error limpiando Cache Storage:', e));
        }
    } catch(e) {
        console.warn('⚠️ Error accediendo a Cache Storage:', e);
    }

    // IndexedDB - limpiar si existe
    if ('indexedDB' in window) {
        indexedDB.databases().then(dbs => {
            dbs.forEach(db => {
                indexedDB.deleteDatabase(db.name);
            });
            console.log('✅ IndexedDB limpiado');
        }).catch(e => console.warn('⚠️ Error limpiando IndexedDB:', e));
    }

    // Configurar jQuery AJAX sin caché
    if (typeof $ !== "undefined") {
        $.ajaxSetup({
            cache: false,
            headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
        });
        console.log('✅ jQuery AJAX configurado sin caché');
    }

    // Configurar fetch API sin caché
    if (window.fetch) {
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            if (args[1]) {
                args[1].cache = 'no-store';
                args[1].headers = {
                    ...args[1].headers,
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                };
            } else {
                args[1] = {
                    cache: 'no-store',
                    headers: {
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    }
                };
            }
            return originalFetch.apply(this, args);
        };
        console.log('✅ Fetch API configurado sin caché');
    }

    // IMPORTANTE: NO DETECTAR REFRESH NI FORZAR RECARGAS
    // Esto estaba causando el bucle infinito
    
    // Agregar timestamps a recursos dinámicamente
    function addTimestampsToResources() {
        const timestamp = Date.now();
        
        // Links CSS
        document.querySelectorAll('link[rel="stylesheet"]').forEach(link => {
            const href = link.getAttribute('href');
            if (href && !href.includes('?')) {
                link.setAttribute('href', href + '?t=' + timestamp);
            }
        });
        console.log('✅ Timestamps agregados a ' + document.querySelectorAll('link[rel="stylesheet"]').length + ' enlaces');

        // Scripts
        document.querySelectorAll('script[src]').forEach(script => {
            const src = script.getAttribute('src');
            if (src && !src.includes('?') && !src.includes('no-cache.js')) {
                script.setAttribute('src', src + '?t=' + timestamp);
            }
        });

        // Forms
        document.querySelectorAll('form').forEach(form => {
            if (form.method.toLowerCase() === 'get') {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = '_t';
                input.value = timestamp;
                form.appendChild(input);
            }
        });
        console.log('✅ Timestamps agregados a ' + document.querySelectorAll('form').length + ' formularios');

        // Imágenes
        document.querySelectorAll('img[src]').forEach(img => {
            const src = img.getAttribute('src');
            if (src && !src.includes('?') && !src.includes('data:')) {
                img.setAttribute('src', src + '?t=' + timestamp);
            }
        });
        console.log('✅ Caché de imágenes limpiado para ' + document.querySelectorAll('img[src]').length + ' imágenes');
    }

    // Aplicar timestamps cuando el DOM esté listo
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', addTimestampsToResources);
    } else {
        addTimestampsToResources();
    }

    console.log('🚫 LIMPIEZA TOTAL DE CACHÉ COMPLETADA');

})();