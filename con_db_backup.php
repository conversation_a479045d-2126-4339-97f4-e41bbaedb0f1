<?php
// Configurar reporte de errores
error_reporting(E_ALL);
// Solo mostrar errores si este archivo se ejecuta directamente
ini_set('display_errors', basename($_SERVER['SCRIPT_FILENAME']) == basename(__FILE__) ? 1 : 0);

// Crear carpeta de logs si no existe
$log_dir = dirname(__FILE__) . '/logs';
if (!is_dir($log_dir)) {
    mkdir($log_dir, 0755, true);
}

// Función para log más detallado
function escribir_log($mensaje, $tipo = 'error') {
    $log_dir = dirname(__FILE__) . '/logs';
    $log_file = $log_dir . '/db_errors.log';
    
    // Asegurarse de que el directorio de logs existe
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    $fecha = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    $url = $_SERVER['REQUEST_URI'] ?? 'Unknown';
    $mensaje_log = "[$fecha] [$tipo] [$ip] [$url] $mensaje" . PHP_EOL;
    
    // Escribir en el log
    error_log($mensaje_log, 3, $log_file);
    
    // Para errores, también escribir en el log de errores de PHP
    if ($tipo == 'error') {
        error_log("DB ERROR: $mensaje");
    }
}

// Solo escribir logs si no estamos en un contexto que requiere JSON limpio
if (basename($_SERVER['SCRIPT_FILENAME']) != 'ControllerGestar.php') {
    escribir_log("Intentando conexión a la base de datos", 'info');
}

// Incluir sistema de configuración multi-entorno
require_once dirname(__FILE__) . '/config.php';

// Configuración de la conexión a la base de datos según el entorno
$envConfig = EnvironmentConfig::getInstance();

// Debug de entorno - definir contexto JSON
$is_json_context = basename($_SERVER['SCRIPT_FILENAME']) == 'ControllerGestar.php';

// Configuración inicial para desarrollo
if ($envConfig->isDevelopment()) {
    // Opciones de conexión para intentar, en orden de preferencia
    $connection_options = [
        // Opción 1: MySQL local XAMPP estándar (con el socket por defecto)
        [
            'host' => 'localhost',
            'port' => 3306,
            'dbname' => 'gestarse_experian',
            'username' => 'root',
            'password' => ''
        ],
        // Opción 1.1: MySQL local XAMPP estándar (con socket alternativo)
        [
            'host' => '127.0.0.1',
            'port' => 3306,
            'dbname' => 'gestarse_experian',
            'username' => 'root',
            'password' => ''
        ],
        // Opción 1.2: MySQL local XAMPP con socket explícito
        [
            'host' => 'mysql',
            'port' => 3306,
            'dbname' => 'gestarse_experian',
            'username' => 'root',
            'password' => ''
        ],
        // Opción 2: MySQL local con puerto alternativo para Windows
        [
            'host' => 'localhost',
            'port' => 3307,
            'dbname' => 'gestarse_experian',
            'username' => 'root',
            'password' => ''
        ],
        // Opción 2.1: MySQL local con IP explícita y puerto alternativo
        [
            'host' => '127.0.0.1',
            'port' => 3307,
            'dbname' => 'gestarse_experian',
            'username' => 'root',
            'password' => ''
        ],
        // Opción 3: Servidor remoto como último recurso
        [
            'host' => '*************',
            'port' => 3306,
            'dbname' => 'gestarse_experian',
            'username' => 'gestarse_ncornejo7_experian',
            'password' => 'N1c0l7as17'
        ]
    ];
    
    // Intentar cada configuración hasta encontrar una que funcione
    $connection_successful = false;
    foreach ($connection_options as $option) {
        if (!$is_json_context) {
            escribir_log("DESARROLLO: Intentando conectar a {$option['host']}:{$option['port']}", 'info');
        }
        
        // Suprimir advertencias durante la prueba de conexión
        try {
            // Establecer un timeout bajo para evitar bloqueos
            $driver = new mysqli_driver();
            $driver->report_mode = MYSQLI_REPORT_OFF; // Desactivar el reporte de errores
            
            // Intentar con una conexión con timeout reducido
            $local_test = new mysqli();
            $local_test->options(MYSQLI_OPT_CONNECT_TIMEOUT, 2); // 2 segundos de timeout
            @$local_test->real_connect($option['host'], $option['username'], $option['password'], $option['dbname'], $option['port'])
            
            if ($local_test && !$local_test->connect_error) {
            // Conexión exitosa, usar esta configuración
            $host = $option['host'];
            $port = $option['port'];
            $dbname = $option['dbname'];
            $username = $option['username'];
            $password = $option['password'];
            
            if (!$is_json_context) {
                escribir_log("DESARROLLO: Conexión exitosa a {$host}:{$port}", 'info');
            }
            
            $local_test->close();
            $connection_successful = true;
            break;
        } else {
            if (!$is_json_context) {
                $error_msg = ($local_test && $local_test->connect_error) ? $local_test->connect_error : "Error desconocido al conectar";
                escribir_log("DESARROLLO: Fallo al conectar a {$option['host']}: {$error_msg}", 'info');
            }
        }
        } catch (Throwable $e) {
            if (!$is_json_context) {
                escribir_log("DESARROLLO: Excepción al conectar a {$option['host']}: " . $e->getMessage(), 'info');
            }
        }
    }
    
    if (!$connection_successful) {
        // Si ninguna conexión funcionó, usar la última opción (remota) de todos modos
        $last_option = end($connection_options);
        $host = $last_option['host'];
        $port = $last_option['port'];
        $dbname = $last_option['dbname'];
        $username = $last_option['username'];
        $password = $last_option['password'];
        
        if (!$is_json_context) {
            escribir_log("ADVERTENCIA: Todas las conexiones fallaron. Usando configuración remota como último recurso.", 'warning');
        }
    }
} else {
    // Configuración para producción
    $host = 'localhost'; // En producción, MySQL suele estar en localhost
    $port = 3306;
    $dbname = 'gestarse_experian';
    $username = 'gestarse_ncornejo7_experian';
    $password = 'N1c0l7as17';
}

// Debug de entorno - solo si no estamos en contexto JSON
if (!$is_json_context) {
    escribir_log("Ambiente de ejecución: " . php_uname(), 'info');
}

// Definir variables globales para conexión
$mysqli = null;
$conn = null;
$conexion = null;
$connection_error = null;

try {
    if (!$is_json_context) {
        escribir_log("Parámetros de conexión: host=$host, dbname=$dbname, user=$username, port=$port", 'info');
    }

    // Intentar conexión con mysqli
    if (!$is_json_context) {
        escribir_log("Intentando conexión a $host:$port...", 'info');
    }
    
    // Aumentar el tiempo de espera de conexión y suprimir advertencias
    // Establecer un timeout para evitar que la conexión se quede colgada
    ini_set('default_socket_timeout', 2); // Reducir a 2 segundos para fallo más rápido
    
    // Configurar opciones avanzadas para manejo de errores
    $driver = new mysqli_driver();
    $driver->report_mode = MYSQLI_REPORT_OFF; // Desactivar el reporte de errores
    
    // Crear la conexión con opciones de timeout
    $mysqli = new mysqli();
    $mysqli->options(MYSQLI_OPT_CONNECT_TIMEOUT, 3); // 3 segundos de timeout
    @$mysqli->real_connect($host, $username, $password, $dbname, $port);
    
    // Si estamos en Windows, probar usando TCP explícitamente si la conexión falla
    if (($mysqli && $mysqli->connect_error) && stripos(PHP_OS, 'win') !== false) {
        escribir_log("Intentando conexión TCP explícita en Windows...", 'info');
        
        // Crear nueva conexión usando TCP explícito
        $mysqli = new mysqli();
        $mysqli->options(MYSQLI_OPT_CONNECT_TIMEOUT, 3);
        @$mysqli->real_connect('127.0.0.1', $username, $password, $dbname, $port);
        
        // Si aún falla, intentar con socket archivo
        if ($mysqli && $mysqli->connect_error) {
            escribir_log("Intentando con socket archivo en Windows...", 'info');
            $mysqli = new mysqli();
            $mysqli->options(MYSQLI_OPT_CONNECT_TIMEOUT, 3);
            @$mysqli->real_connect('localhost', $username, $password, $dbname, $port, 'C:\xampp\mysql\mysql.sock');
        }
    }
    
    // Verificar si hay error de conexión
    if (!$mysqli || $mysqli->connect_error) {
        $connection_error = "Error de conexión a la base de datos: {$mysqli->connect_error} (Código: {$mysqli->connect_errno})";
        escribir_log($connection_error, 'error');
        
        // Si estamos en desarrollo y la conexión falla, intentar con la conexión remota como último recurso
        if ($envConfig->isDevelopment() && $host !== '*************') {
            escribir_log("Intentando conexión remota como último recurso", 'info');
            
            // Configuración remota
            $host = '*************';
            $port = 3306;
            $dbname = 'gestarse_experian';
            $username = 'gestarse_ncornejo7_experian';
            $password = 'N1c0l7as17';
            
            // Intentar nueva conexión
            $mysqli = @new mysqli($host, $username, $password, $dbname, $port);
            
            if ($mysqli->connect_error) {
                $connection_error = "Error de conexión remota: {$mysqli->connect_error} (Código: {$mysqli->connect_errno})";
                escribir_log($connection_error, 'error');
            } else {
                $connection_error = null; // Limpiar el error si la conexión remota tuvo éxito
                escribir_log("Conexión remota establecida correctamente", 'info');
            }
        }
    } else {
        if (!$is_json_context) {
            escribir_log("Conexión mysqli creada", 'info');
        }
    }
    
    // Si aún hay error de conexión después de los intentos, manejar el error
    if ($connection_error) {
        // Crear un objeto compatible para evitar errores fatales cuando se referencie
        $mysqli = new class {
            public $connect_error;
            public $error;
            public $connect_errno = -1;
            
            public function __construct($err) {
                $this->connect_error = $err;
                $this->error = "No se pudo establecer conexión";
            }
            
            // Métodos dummy para compatibilidad
            public function query() { return false; }
            public function real_escape_string($str) { return $str; }
            public function close() { return true; }
        };
        $mysqli = new $mysqli($connection_error);
        
        // Asignar a variables globales para mantener compatibilidad
        $conn = $mysqli;
        $conexion = $mysqli;
        
        // Si este archivo se ejecuta directamente, mostrar error
        if (basename($_SERVER['SCRIPT_FILENAME']) == basename(__FILE__)) {
            die("Error de conexión: " . $connection_error);
        }
        
        // Si este archivo se incluye desde un archivo que espera JSON, mostrar error en formato JSON
        if (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => $connection_error
            ]);
            exit;
        }
        
        // No lanzar excepción para permitir que la página se cargue con un mensaje de error
        escribir_log("Continuando sin conexión a la base de datos", 'warning');
    } else {
        // Asignar la conexión a variables globales para mantener compatibilidad
        $conn = $mysqli;
        $conexion = $mysqli;
        
        if (!$is_json_context) {
            escribir_log("Conexión establecida, verificando con consulta simple", 'info');
        }

        // Verificar la conexión con una consulta simple
        if (!$mysqli->query("SELECT 1")) {
            escribir_log("Error en la consulta de verificación: " . $mysqli->error, 'error');
        } else if (!$is_json_context) {
            escribir_log("Conexión verificada correctamente", 'info');
        }
    }
} catch (Exception $e) {
    $error_message = "Excepción en conexión: " . $e->getMessage();
    escribir_log($error_message, 'error');

    // Si este archivo se incluye desde un archivo que espera JSON, mostrar error en formato JSON
    if (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => $error_message
        ]);
        exit;
    }
    
    // Crear un objeto mysqli vacío para evitar errores fatales
    $mysqli = new stdClass();
    $mysqli->connect_error = $error_message;
    $mysqli->error = "Excepción en la conexión";
    
    // Asignar a variables globales para mantener compatibilidad
    $conn = $mysqli;
    $conexion = $mysqli;
    
    // No lanzar excepción para permitir que la página se cargue con un mensaje de error
    escribir_log("Continuando sin conexión a la base de datos debido a excepción", 'warning');
}

// Función para obtener una conexión PDO
// IMPORTANTE: Renombrada para evitar conflicto con la función en form_experian2.php
function createPDOConnection() {
    global $mysqli, $conn, $host, $dbname, $username, $password;

    // Si ya existe una conexión mysqli, crear y devolver una conexión PDO equivalente
    if (isset($mysqli) && $mysqli instanceof mysqli && !$mysqli->connect_error) {
        try {
            return new PDO(
                "mysql:host=$host;dbname=$dbname;charset=utf8",
                $username,
                $password,
                array(PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION)
            );
        } catch (PDOException $e) {
            error_log("Error creando conexión PDO desde mysqli existente: " . $e->getMessage());
            return null;
        }
    }

    // Si no hay conexión existente, crear una nueva
    try {
        $pdo = new PDO(
            "mysql:host=$host;dbname=$dbname;charset=utf8",
            $username,
            $password,
            array(PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION)
        );
        return $pdo;
    } catch (PDOException $e) {
        error_log("Error creando nueva conexión a la base de datos: " . $e->getMessage());
        return null;
    }
}

// Alias de compatibilidad para scripts existentes que usen getDBConnection()
if (!function_exists('getDBConnection')) {
    function getDBConnection() {
        return createPDOConnection();
    }
}

// Función para cerrar la conexión
function cerrarConexion() {
    global $mysqli, $conn;
    
    // Verificar si la conexión mysqli existe y está activa
    if (isset($mysqli) && $mysqli instanceof mysqli) {
        try {
            // Verificar si la conexión está activa antes de cerrarla
            // Usar ping con suppress de warnings para evitar errores en conexiones rotas
            $can_close = false;
            
            // Intentar verificar si la conexión está activa
            if (method_exists($mysqli, 'ping')) {
                try {
                    // Suprimir errores durante la verificación
                    $can_close = @$mysqli->ping();
                } catch (\Throwable $e) {
                    // Si hay una excepción, no podemos cerrar limpiamente
                    $can_close = false;
                    escribir_log("No se puede cerrar la conexión: ya está cerrada o en error", 'info');
                }
            }
            
            // Si la conexión está activa, cerrarla
            if ($can_close) {
                $mysqli->close();
                // Solo escribir log si no estamos en contexto JSON
                if (basename($_SERVER['SCRIPT_FILENAME']) != 'ControllerGestar.php') {
                    escribir_log("Conexión cerrada correctamente", 'info');
                }
            }
        } catch (\Throwable $e) {
            // Capturar y registrar cualquier error al cerrar la conexión
            escribir_log("Error al cerrar la conexión: " . $e->getMessage(), 'error');
        }
    }
    
    // Limpiar la variable para evitar intentos de cierre posteriores
    $mysqli = null;
}

// Registrar la función para cerrar la conexión al finalizar el script
register_shutdown_function('cerrarConexion');

// Solo mostrar el mensaje si este archivo se ejecuta directamente y no como inclusión
if (basename($_SERVER['SCRIPT_FILENAME']) == basename(__FILE__)) {
    echo "Conexión a la base de datos establecida correctamente";
}