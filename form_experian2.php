<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Incluir utilidades de caché ANTES de cualquier salida
require_once 'cache_utils.php';

// LIMPIAR COMPLETAMENTE TODO EL CACHÉ
clear_all_cache();

// Configuración de encabezados HTTP - SIN CACHÉ TOTAL
header('Content-Type: text/html; charset=UTF-8');

// Aplicar headers ultra agresivos para eliminar TODO el caché
no_cache_headers();
dev_no_cache_headers();

// Configuraciones de sesión persistente (24 horas) - Estrategia TQW
ini_set('session.use_cookies', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_lifetime', 86400); // 24 horas
ini_set('session.gc_maxlifetime', 86400);  // 24 horas

// Iniciar sesión si no está activa
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verificar si venimos de navegación hacia atrás
$referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
if (strpos($referer, $_SERVER['HTTP_HOST']) !== false) {
    // Regenerar ID de sesión sin destruir datos
    session_regenerate_id(false);
}

// Establecer última actualización para ETag
$_SESSION['last_update'] = time();

// Inicializar variables importantes
$es_admin = false; // Variable que indica si el usuario es administrador

// Verificación mejorada de autenticación
function checkAuth() {
    if (!isset($_SESSION['usuario']) || empty($_SESSION['usuario'])) {
        // Registrar el intento fallido
        error_log("[" . date('Y-m-d H:i:s') . "] Error de autenticación - Usuario no identificado - IP: " . $_SERVER['REMOTE_ADDR']);

        // Redireccionar a login con mensaje de error
        $errorMsg = urlencode("Usuario no autenticado. Inicie sesión para continuar.");
        header('Location: ' . version_url('login.php?error=' . $errorMsg));
        exit;
    }

    // Renovar la sesión para evitar su caducidad prematura
    $_SESSION['last_activity'] = time();
    return true;
}

// Verificar si el usuario está autenticado
checkAuth();

// Incluir el archivo de conexión
require_once 'con_db.php';

// Variables para controlar el estado de la conexión
$db_connection_ok = true;
$offline_mode = false;

// Verificar si la conexión fue exitosa
if (isset($mysqli) && is_object($mysqli) && !($mysqli instanceof stdClass) && !$mysqli->connect_error) {
    $db_connection_ok = true;
    $connection_error_message = "";
} else {
    $db_connection_ok = false;
    $connection_error_message = isset($mysqli->connect_error) ? $mysqli->connect_error : "Error de conexión desconocido";
    error_log("Error de conexión en form_experian2.php: " . $connection_error_message);
}

// Usar la función createPDOConnection() del archivo con_db.php
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Formulario de Cliente - SIN CACHÉ</title>

    <!-- ANTI-CACHÉ TOTAL - NO GUARDAR NADA -->
    <?php echo no_cache_meta(); ?>

    <!-- JavaScript para limpiar caché del navegador -->
    <?php echo browser_cache_clear_js(); ?>
    
    <!-- Estilos con versiones anti-caché -->
    <link rel="stylesheet" href="<?php echo version_url('css/form_experian.css'); ?>">
    <link rel="stylesheet" href="<?php echo version_url('css/table-sort.css'); ?>">
    <link rel="stylesheet" href="<?php echo version_url('css/notifications.css'); ?>">
    <!-- Agregar Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Cargar jQuery primero -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Fallback para jQuery en caso de problemas de conexión -->
    <script>
        window.addEventListener('DOMContentLoaded', function() {
            if (typeof jQuery === 'undefined') {
                // Crear un script element manualmente
                var script = document.createElement('script');
                script.src = '<?php echo version_url("js/jquery-3.7.1.min.js"); ?>';
                document.head.appendChild(script);
                console.log('Cargando jQuery desde servidor local debido a problemas de conexión');
            }
        });
    </script>
    <!-- Agregar SheetJS (versión completa y estable) -->
    <script src="https://unpkg.com/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <!-- Sistema de notificaciones -->
    <script src="<?php echo version_url('js/notifications.js'); ?>"></script>
    <!-- Lógica del formulario centralizada -->
    <script src="<?php echo version_url('js/form_experian.js'); ?>"></script>
    
    <!-- Inyectar variables de estado de conexión para JS -->
    <script>
        // Comunicar el estado de la conexión a JavaScript
        window.dbConnectionOk = <?php echo $db_connection_ok ? 'true' : 'false'; ?>;
        <?php if (!$db_connection_ok): ?>
        window.dbConnectionError = "<?php echo addslashes($connection_error_message); ?>";
        <?php endif; ?>
    </script>
</head>
<body>
<?php
// Obtener el nombre del usuario desde la base de datos
$nombre_usuario = '';
try {
    // Verificar estado de conexión global
    if (!$db_connection_ok) {
        throw new Exception("No hay conexión a la base de datos: " . $connection_error_message);
    }
    
    // Verificar que la función existe antes de usarla
    if (function_exists('createPDOConnection')) {
        $connection = createPDOConnection();
        if (!$connection) {
            throw new Exception("No se pudo establecer la conexión PDO");
        }
    } else {
        // Alternativa si la función no está disponible
        $connection = new PDO(
            "mysql:host=localhost;dbname=gestarse_experian;charset=utf8",
            'gestarse_ncornejo7_experian',
            'N1c0l7as17',
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION, 
             PDO::ATTR_TIMEOUT => 5, // Timeout de 5 segundos
             PDO::ATTR_PERSISTENT => false]
        );
    }
    if ($connection) {
        $query = "SELECT correo, COALESCE(nombre_usuario, SUBSTRING_INDEX(correo, '@', 1)) AS nombre_usuario FROM tb_experian_usuarios WHERE id = ?";
        $stmt = $connection->prepare($query);
        $stmt->execute([$_SESSION['usuario_id']]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($result && isset($result['nombre_usuario']) && !empty($result['nombre_usuario'])) {
            $nombre_usuario = $result['nombre_usuario'];
            // Usar el nombre completo sin dividirlo
            $primer_nombre = $nombre_usuario;
        } else {
            // Si no hay nombre_usuario, usar el correo como alternativa
            $nombre_usuario = $_SESSION['usuario'];
            $primer_nombre = $nombre_usuario;
        }
    }
} catch (Exception $e) {
    error_log("Error al obtener nombre de usuario: " . $e->getMessage());
    // En caso de error, usar el correo como alternativa
    $nombre_usuario = $_SESSION['usuario'] ?? 'Usuario';
    $primer_nombre = $nombre_usuario;
    
    // Mostrar un mensaje de advertencia si es un problema de conexión
    if ($e->getMessage() === "No hay conexión a la base de datos: " . $connection_error_message) {
        $offline_mode = true;
    }
}
?>
<div class="site-header">
    <div class="header-container">
        <div class="logo-container">
            <img src="<?php echo version_url('img/img_experian/logo.jpg'); ?>" alt="Logo Experian" class="header-logo">
        </div>
        <div class="user-info">
            <span class="user-name"><?php echo htmlspecialchars($primer_nombre); ?></span>
        </div>
        <a href="logout.php" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Salir</a>
    </div>
</div>

<?php if ($offline_mode): ?>
<!-- Alerta de modo fuera de línea -->
<div id="offline-mode-alert" style="background-color: #fff3cd; color: #856404; padding: 12px; margin: 0; text-align: center; border-bottom: 1px solid #ffeeba; font-weight: bold;">
    <i class="fas fa-exclamation-triangle"></i> Modo sin conexión a la base de datos. Algunas funciones estarán limitadas.
</div>
<?php endif; ?>

<!-- Loader global para indicar carga -->
<div id="global-loader" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(255,255,255,0.8); z-index:9999; text-align:center; padding-top:20%;">
    <div style="display:inline-block; width:50px; height:50px; border:5px solid #f3f3f3; border-top:5px solid #0046ad; border-radius:50%; animation:spin 1s linear infinite;"></div>
    <p style="margin-top:10px; color:#0046ad; font-weight:bold;">Cargando...</p>
</div>

<style>
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Estilos para campos con errores */
.error-field {
    border: 2px solid #e74c3c !important;
    background-color: #fdf2f2 !important;
    box-shadow: 0 0 5px rgba(231, 76, 60, 0.3) !important;
}

.error-message {
    color: #e74c3c;
    font-size: 12px;
    margin-top: 2px;
    font-weight: 500;
}

/* Estilos para el resumen de errores */
#error-summary {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 15px;
    margin: 15px 0;
    border-radius: 4px;
    position: relative;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Estilos para indicador de carga */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Mejorar visibilidad de campos requeridos */
input[required], select[required], textarea[required] {
    border-left: 3px solid #3498db;
}

input[required]:focus, select[required]:focus, textarea[required]:focus {
    border-left: 3px solid #2980b9;
}

/* Estilos para overlay de carga */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    z-index: 9999;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #0046ad;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    margin-top: 10px;
    color: #0046ad;
    font-weight: bold;
    font-size: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

<!-- Error Monitor: Se mostrará solo si hay errores -->
<div id="error-monitor" style="display: none; background-color: #ffebee; border-left: 4px solid #f44336; margin: 10px 0; padding: 10px 15px;">
    <div style="display: flex; justify-content: space-between; align-items: center;">
        <h3 style="margin: 0; color: #d32f2f; font-size: 16px;">Error Detectado</h3>
        <button onclick="document.getElementById('error-details').style.display = document.getElementById('error-details').style.display === 'none' ? 'block' : 'none';" 
                style="background: none; border: none; color: #d32f2f; cursor: pointer; font-weight: bold;">
            <i class="fas fa-chevron-down"></i> Detalles
        </button>
    </div>
    <p id="error-message" style="margin: 5px 0; font-weight: 500;"></p>
    <div id="error-details" style="display: none; margin-top: 10px; padding: 10px; background-color: #fff; border: 1px solid #ffcdd2; border-radius: 4px; font-family: monospace; font-size: 12px; white-space: pre-wrap; max-height: 200px; overflow-y: auto;"></div>
</div>



<div class="tabs-container">
    <!-- Contenido principal -->
    <div class="main-content" style="margin-bottom: 90px;">

    <!-- Tab Contenido: Formulario -->
    <div id="form-tab" class="tab-content" style="overflow-y: auto; margin-bottom: 65px;">
        <?php if (!$db_connection_ok): ?>
        <div class="alert alert-warning" style="background-color: #fff3cd; color: #856404; padding: 15px; margin-bottom: 20px; border: 1px solid #ffeeba; border-radius: 4px;">
            <h4 style="margin-top: 0;"><i class="fa fa-exclamation-triangle"></i> Advertencia: Modo sin conexión</h4>
            <p>No se pudo establecer conexión con la base de datos. El formulario se muestra en modo de visualización, pero algunas funciones pueden estar limitadas.</p>
            <p><strong>Detalles técnicos:</strong> <?php echo htmlspecialchars($connection_error_message); ?></p>
            <p><strong>Recomendación:</strong> Verifique que el servidor MySQL esté en ejecución o contacte al administrador del sistema.</p>
        </div>
        <?php endif; ?>
        <form id="formExperian" method="POST" action="guardar_formulario.php" enctype="multipart/form-data">            <div class="container">
                <header style="display: flex; justify-content: space-between; align-items: center;">
                    <h1>Formulario de Registro de Cliente</h1>
                    <button type="button" onclick="llenarTodoElFormulario();" style="background-color: #4CAF50; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; font-size: 14px;">
                        <i class="fa fa-magic"></i> Llenar Todo el Formulario
                    </button>
                </header>

                <!-- Indicador de pasos -->
                <div class="steps-container">
                    <div class="progress-line">
                        <div class="fill"></div>
                    </div>
                    <div class="step-indicator active" data-step="1">1. Identificación del Cliente</div>
                    <div class="step-indicator" data-step="2">2. Datos de Contactos</div>
                    <div class="step-indicator" data-step="3">3. Servicios y Transacciones</div>
                </div>

                <!-- Sección 1: Identificación del cliente -->
                <div class="section-container active" id="section1">
                    <div class="section-header">1. IDENTIFICACIÓN DEL CLIENTE</div>

                    <table>
                        <tr>
                            <td class="label">Tipo de Cliente</td>
                            <td class="input-cell">
                                <select name="tipo_cliente" title="Tipo de Cliente" required>
                                    <option value="">Seleccione...</option>
                                    <option>Cliente Vigente</option>
                                    <option>Cliente No vigente</option>
                                </select>
                            </td>
                            <td class="label">Rut</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="rut"
                                    title="Rut"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12"
                                    required>
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Razón Social</td>
                            <td class="input-cell">
                                <input type="text" name="razon_social" title="Razón Social" required>
                            </td>
                            <td class="label">Nombre Representante Legal 1</td>
                            <td class="input-cell">
                                <input type="text" name="nombre_representante1" title="Nombre Representante Legal 1" required>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Rut Representante 1</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="rut_representante1"
                                    title="Rut Representante 1"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                            <td class="label">Nombre Representante Legal 2</td>
                            <td class="input-cell"><input type="text" name="nombre_representante2" title="Nombre Representante Legal 2"></td>
                        </tr>
                        <tr>
                            <td class="label">Rut Representante 2</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="rut_representante2"
                                    title="Rut Representante 2"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                            <td class="label">Nombre Representante Legal 3</td>
                            <td class="input-cell"><input type="text" name="nombre_representante3" title="Nombre Representante Legal 3"></td>
                        </tr>
                        <tr>
                            <td class="label">Rut Representante 3</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="rut_representante3"
                                    title="Rut Representante 3"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                            <td class="label">Sistema Creación de Empresa</td>
                            <td class="input-cell">
                                <select name="sistema_creacion" title="Sistema Creación de Empresa">
                                    <option>Tradicional</option>
                                    <option>Empresa por un día</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Fecha de creación</td>
                            <td class="input-cell">
                                <input type="date" name="fecha_creacion" title="Fecha de creación" required>
                            </td>
                            <td class="label">Notaría</td>
                            <td class="input-cell"><input type="text" name="notaria" title="Notaría"></td>
                        </tr>
                        <tr>
                            <td class="label">Actividad Económica SII</td>
                            <td class="input-cell"><input type="number" name="actividad_economica" title="Actividad Económica SII"></td>
                            <td class="label">Fecha de Constitución</td>
                            <td class="input-cell">
                                <input type="date" name="fecha_constitucion" title="Fecha de Constitución" required>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Dirección</td>
                            <td class="input-cell"><input type="text" name="direccion" title="Dirección"></td>
                            <td class="label">Comuna</td>
                            <td class="input-cell"><input type="text" name="comuna" title="Comuna"></td>
                        </tr>
                        <tr>
                            <td class="label">Página Web</td>
                            <td class="input-cell"><input type="text" name="pagina_web" title="Página Web"></td>
                            <td class="label">Correo Electrónico contacto</td>
                            <td class="input-cell">
                                <input type="email" name="email" title="Correo Electrónico contacto" required>
                                <div class="info-message">Formato: <EMAIL></div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Teléfono</td>
                            <td class="input-cell">
                                <input type="tel" name="telefono" title="Teléfono"
                                    pattern="[0-9]{9}"
                                    maxlength="9"
                                    placeholder="912345678"
                                    required>
                                <div class="info-message">Debe contener 9 dígitos</div>
                            </td>
                            <td class="label">Clasificación de Cliente SII</td>
                            <td class="input-cell">
                                <select name="clasificacion_sii" title="Clasificación de Cliente SII">
                                    <option>Nuevo</option>
                                    <option>Antiguo</option>
                                </select>
                            </td>
                        </tr>
                    </table>

                    <div class="nav-buttons">
                        <button type="button" class="btn-test" onclick="llenarSeccion1();" style="background-color: #ff9800; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                            <i class="fa fa-magic"></i> Llenar Automáticamente
                        </button>
                        <button type="button" class="btn-next" data-next="2">Siguiente</button>
                    </div>
                    
                    
                </div>

                <!-- Sección 2: Datos de Contactos -->
                <div class="section-container" id="section2">
                    <div class="section-header">2. DATOS DE CONTACTOS</div>

                    <table>
                        <tr>
                            <td class="label">Contacto Nombre</td>
                            <td class="input-cell"><input type="text" name="contacto_nombre" title="Contacto Nombre"></td>
                            <td class="label">Rut</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="contacto_rut"
                                    title="Rut"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Teléfono</td>
                            <td class="input-cell">
                                <input type="text" name="contacto_telefono" title="Teléfono"
                                    pattern="[0-9]{9}"
                                    maxlength="9"
                                    placeholder="912345678">
                                <div class="info-message">Debe contener 9 dígitos</div>
                            </td>
                            <td class="label">Correo Electrónico</td>
                            <td class="input-cell">
                                <input type="email" name="contacto_email" title="Correo Electrónico">
                                <div class="info-message">Formato: <EMAIL></div>
                            </td>
                        </tr>
                        <td class="label">2.1 DATOS DE BACKUP</td>
                        <tr>
                            <td class="label">Contacto Backup Nombre</td>
                            <td class="input-cell"><input type="text" name="contacto_backup_nombre" title="Contacto Backup Nombre"></td>
                            <td class="label">Rut</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="contacto_backup_rut"
                                    title="Rut"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Teléfono</td>
                            <td class="input-cell">
                                <input type="text" name="contacto_backup_telefono" title="Teléfono"
                                    pattern="[0-9]{9}"
                                    maxlength="9"
                                    placeholder="912345678">
                                <div class="info-message">Debe contener 9 dígitos</div>
                            </td>
                            <td class="label">Correo Electrónico</td>
                            <td class="input-cell">
                                <input type="email" name="contacto_backup_email" title="Correo Electrónico">
                                <div class="info-message">Formato: <EMAIL></div>
                            </td>
                        </tr>
                    </table>

                    <div class="nav-buttons">
                        <button type="button" class="btn-prev" data-prev="1">Anterior</button>
                        <button type="button" class="btn-test" onclick="llenarSeccion2();" style="background-color: #ff9800; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                            <i class="fa fa-magic"></i> Llenar Automáticamente
                        </button>
                        <button type="button" class="btn-next" data-next="3">Siguiente</button>
                    </div>
                </div>

                <!-- Sección 3: Servicios y Nivel de Transacciones -->
                <div class="section-container" id="section3">
                    <div class="section-header">3. SERVICIOS Y NIVEL DE TRANSACCIONES / PUNTOS</div>

                    <!-- Subsección 3.1: Publicación de Morosos -->
                    <div class="subsection-header">3.1 PUBLICACIÓN DE MOROSOS</div>
                    <table>
                        <tr>
                            <td class="label">Plan</td>
                            <td class="input-cell">
                                <select name="morosos_plan" id="morosos_plan" title="Plan Publicación de Morosos">
                                    <option value="">Seleccione...</option>
                                    <option>XS</option>
                                    <option>S</option>
                                    <option>M</option>
                                    <option>L</option>
                                    <option>XL</option>
                                </select>
                            </td>
                            <td class="label">Número de Consultas</td>
                            <td class="input-cell">
                                <input type="text" name="morosos_consultas" id="morosos_consultas" title="Número de Consultas"  readonly>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">UF Mensual</td>
                            <td class="input-cell">
                                <input type="number" name="morosos_uf" id="morosos_uf" step="0.01" title="UF Mensual" placeholder="0.00" readonly>
                            </td>
                            <td class="label">% de descuento</td>
                            <td class="input-cell">
                                <select name="morosos_descuento" title="% de descuento">
                                    <option value="0">0%</option>
                                    <option value="10">10%</option>
                                    <option value="20">20%</option>
                                    <option value="30">30%</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Nuevo Valor</td>
                            <td class="input-cell">
                                <input type="number" name="morosos_nuevo_valor" step="0.01" title="Nuevo Valor" placeholder="0.00" readonly>
                            </td>
                            <td></td>
                            <td></td>
                        </tr>
                    </table>

                    <!-- Subsección 3.2: Informe Advanced SME -->
                    <div class="subsection-header">3.2 INFORME ADVANCED SME</div>
                    <table>
                        <tr>
                            <td class="label">Plan</td>
                            <td class="input-cell">
                                <select name="advanced_plan" title="Plan Advanced SME">
                                    <option value="">Seleccione...</option>
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                    <option value="200">200</option>
                                    <option value="300">300</option>
                                    <option value="400">400</option>
                                    <option value="500">500</option>
                                    <option value="1000">1.000</option>
                                    <option value="2000">2.000</option>
                                    <option value="3000">3.000</option>
                                    <option value="4000">4.000</option>
                                    <option value="5000">5.000</option>
                                </select>
                            </td>
                            <td class="label"> UF / transacción</td>
                            <td class="input-cell">
                                <input type="text" name="advanced_consultas" title="Número de Consultas"  readonly>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">UF Mensual</td>
                            <td class="input-cell">
                                <input type="number" name="advanced_uf" step="0.01" title="UF Mensual" placeholder="0.00" readonly>
                            </td>
                            <td class="label">% de descuento</td>
                            <td class="input-cell">
                                <select name="advanced_descuento" title="% de descuento">
                                    <option value="0">0%</option>
                                    <option value="5">5%</option>
                                    <option value="10">10%</option>
                                    <option value="15%">15%</option>
                                    <option value="20">20%</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Nuevo Valor</td>
                            <td class="input-cell">
                                <input type="number" name="advanced_nuevo_valor" step="0.01" title="Nuevo Valor" placeholder="0.00" readonly>
                            </td>
                            <td></td>
                            <td></td>
                        </tr>
                    </table>

                    <!-- Subsección 3.3: Uso de Claves -->
                    <div class="subsection-header">3.3 PARA USO DE CLAVES</div>
                    <table>
                        <tr>
                            <td class="label">Nombre</td>
                            <td class="input-cell">
                                <input type="text" name="clave_nombre" title="Nombre para uso de claves">
                            </td>
                            <td class="label">Rut</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="clave_rut"
                                    title="Rut para uso de claves"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Correo</td>
                            <td class="input-cell">
                                <input type="email" name="clave_email" title="Correo para uso de claves">
                                <div class="info-message">Formato: <EMAIL></div>
                            </td>
                            <td class="label">Teléfono</td>
                            <td class="input-cell">
                                <input type="text" name="clave_telefono" title="Teléfono para uso de claves"
                                    pattern="[0-9]{9}"
                                    maxlength="9"
                                    placeholder="912345678">
                                <div class="info-message">Debe contener 9 dígitos</div>
                            </td>
                        </tr>
                    </table>

                    <!-- Subsección 3.4: Backup de Claves -->
                    <div class="subsection-header">3.4 BACKUP DE CLAVES</div>
                    <table>
                        <tr>
                            <td class="label">Nombre</td>
                            <td class="input-cell">
                                <input type="text" name="backup_clave_nombre" title="Nombre backup de claves">
                            </td>
                            <td class="label">Rut</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="backup_clave_rut"
                                    title="Rut backup de claves"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Correo</td>
                            <td class="input-cell">
                                <input type="email" name="backup_clave_email" title="Correo backup de claves">
                                <div class="info-message">Formato: <EMAIL></div>
                            </td>
                            <td class="label">Teléfono</td>
                            <td class="input-cell">
                                <input type="text" name="backup_clave_telefono" title="Teléfono backup de claves"
                                    pattern="[0-9]{9}"
                                    maxlength="9"
                                    placeholder="912345678">
                                <div class="info-message">Debe contener 9 dígitos</div>
                            </td>
                        </tr>
                    </table>

                    <!-- Nueva Subsección 3.5: Documentos -->
                    <div class="subsection-header">3.5 DOCUMENTOS</div>
                    <table>
                        <tr>
                            <td class="label">Carnet de Identidad (CI)</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_ci" id="archivo_ci" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Erut</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_erut" id="archivo_erut" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Extracto</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_extracto" id="archivo_extracto" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">CI Frente</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_ci_frente" id="archivo_ci_frente" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">CI Detrás</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_ci_detras" id="archivo_ci_detras" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Carpeta Tributaria</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_carpeta_tributaria" id="archivo_carpeta_tributaria" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Consulta de Terceros</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_consulta_terceros" id="archivo_consulta_terceros" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                    </table>

                    <div class="nav-buttons">
                        <button type="button" class="btn-prev" data-prev="2">Anterior</button>
                        <button type="button" class="btn-test" onclick="llenarSeccion3();" style="background-color: #ff9800; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                            <i class="fa fa-magic"></i> Llenar Automáticamente
                        </button>
                        <button type="submit" class="btn-submit">Guardar Formulario</button>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Tab Contenido: Tabla -->
    <?php if ($_SESSION['usuario_id'] == 4): ?>
    <div id="table-tab" class="tab-content">
        <div id="table-container">
            <h2>Registros de Clientes</h2>
            <button id="exportClients" class="export-button">
                <i class="fa fa-download"></i> Descargar Registros
            </button>
            <div class="table-controls">
                <input type="text" class="table-search" id="tableSearch" placeholder="Buscar...">
            </div>
            <div class="table-wrapper" style="overflow-x: auto; width: 100%; display: block;">
                <table id="user-table" style="min-width: 100%; width: auto; border-collapse: separate; border-spacing: 0;">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Tipo Cliente</th>
                            <th>RUT</th>
                            <th>Razón Social</th>
                            <th>Nombre Representante 1</th>
                            <th>RUT Representante 1</th>
                            <th>Nombre Representante 2</th>
                            <th>RUT Representante 2</th>
                            <th>Nombre Representante 3</th>
                            <th>RUT Representante 3</th>
                            <th>Sistema Creación</th>
                            <th>Fecha Creación</th>
                            <th>Notaría</th>
                            <th>Actividad Económica</th>
                            <th>Fecha Constitución</th>
                            <th>Dirección</th>
                            <th>Comuna</th>
                            <th>Página Web</th>
                            <th>Email</th>
                            <th>Teléfono</th>
                            <th>Clasificación SII</th>
                            <th>Contacto Nombre</th>
                            <th>Contacto RUT</th>
                            <th>Contacto Teléfono</th>
                            <th>Contacto Email</th>
                            <th>Contacto Backup Nombre</th>
                            <th>Contacto Backup RUT</th>
                            <th>Contacto Backup Teléfono</th>
                            <th>Contacto Backup Email</th>
                            <th>Morosos Plan</th>
                            <th>Morosos Consultas</th>
                            <th>Morosos UF</th>
                            <th>Morosos Descuento</th>
                            <th>Morosos Nuevo Valor</th>
                            <th>Advanced Plan</th>
                            <th>Advanced Consultas</th>
                            <th>Advanced UF</th>
                            <th>Advanced Descuento</th>
                            <th>Advanced Nuevo Valor</th>
                            <!-- Columnas para claves de usuario -->
                            <th>Nombre Usuario Clave</th>
                            <th>RUT Usuario Clave</th>
                            <th>Email Usuario Clave</th>
                            <th>Teléfono Usuario Clave</th>
                            <th>Nombre Backup Clave</th>
                            <th>RUT Backup Clave</th>
                            <th>Email Backup Clave</th>
                            <th>Teléfono Backup Clave</th>
                            <!-- Nuevas columnas para documentos -->
                            <th>CI</th>
                            <th>ERUT</th>
                            <th>Extracto</th>
                            <th>CI Frente</th>
                            <th>CI Detrás</th>
                            <th>Carpeta Tributaria</th>
                            <th>Consulta Terceros</th>
                            <th>Fecha Creación Registro</th>
                            <th>ID Usuario</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        try {
                            // Verificar que la función existe antes de usarla
                            if (function_exists('createPDOConnection')) {
                                $connection = createPDOConnection();
                            } else {
                                // Alternativa si la función no está disponible
                                $connection = new PDO(
                                    "mysql:host=localhost;dbname=gestarse_experian;charset=utf8",
                                    'gestarse_ncornejo7_experian',
                                    'N1c0l7as17',
                                    [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
                                );
                            }
                            if ($connection) {
                                $query = "SELECT * FROM form_experian ORDER BY id DESC";
                                $stmt = $connection->prepare($query);
                                $stmt->execute();
                                $clientes = $stmt->fetchAll(PDO::FETCH_ASSOC);

                                if (empty($clientes)) {
                                    echo '<tr><td colspan="54" class="no-data">No hay registros disponibles</td></tr>';
                                } else {
                                    foreach ($clientes as $cliente) {
                                        echo '<tr>';
                                        foreach ($cliente as $valor) {
                                            echo '<td>' . htmlspecialchars($valor ?? '') . '</td>';
                                        }
                                        echo '</tr>';
                                    }
                                }
                            }
                        } catch (Exception $e) {
                            echo '<tr><td colspan="54" class="error-data">Error al cargar los datos: ' . htmlspecialchars($e->getMessage()) . '</td></tr>';
                            error_log("Error en la consulta de clientes: " . $e->getMessage());
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Tab Contenido: Nueva Opción -->
<div id="new-tab" class="tab-content active" style="margin-bottom: 70px; overflow-y: visible;">
    <div class="container">

        <!-- Modal para el formulario de prospecto -->
        <div id="prospectoModal" class="modal">
            <div class="modal-content">
                <span class="close-modal">&times;</span>
                <h2>Registro de prospecto</h2>



                <?php
                // Obtener el nombre del usuario desde la base de datos
                $nombre_usuario = '';
                try {
                    // Verificar que la función existe antes de usarla
                    if (function_exists('createPDOConnection')) {
                        $connection = createPDOConnection();
                    } else {
                        // Alternativa si la función no está disponible
                        $connection = new PDO(
                            "mysql:host=localhost;dbname=gestarse_experian;charset=utf8",
                            'gestarse_ncornejo7_experian',
                            'N1c0l7as17',
                            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
                        );
                    }
                    if ($connection) {
                        $query = "SELECT correo, COALESCE(nombre_usuario, SUBSTRING_INDEX(correo, '@', 1)) AS nombre_usuario FROM tb_experian_usuarios WHERE id = ?";
                        $stmt = $connection->prepare($query);
                        $stmt->execute([$_SESSION['usuario_id']]);
                        $result = $stmt->fetch(PDO::FETCH_ASSOC);
                        if ($result && isset($result['nombre_usuario'])) {
                            $nombre_usuario = $result['nombre_usuario'];
                        } else {
                            // Si no hay nombre_usuario, usar el correo como alternativa
                            $nombre_usuario = $_SESSION['usuario'];
                        }
                    }
                } catch (Exception $e) {
                    error_log("Error al obtener nombre de usuario: " . $e->getMessage());
                    // En caso de error, usar el correo como alternativa
                    $nombre_usuario = $_SESSION['usuario'];
                }
                ?>

                <form id="formEjecutivos" method="POST" action="guardar_prospecto.php">
                <div class="section-header">DATOS DEL PROSPECTO</div>

                <table>
                    <tr>
                        <td class="label">Nombre Ejecutivo</td>
                        <td class="input-cell">
                            <input type="text" name="nombre_prospecto" title="Nombre Prospecto" value="<?php echo htmlspecialchars($nombre_usuario); ?>" readonly required>
                        </td>
                        <td class="label">Rut</td>
                        <td class="input-cell">
                            <input type="text"
                                name="rut_ejecutivo"
                                title="Rut"
                                class="rut-input"
                                pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                placeholder="12.345.678-9"
                                maxlength="12"
                                required>
                            <div class="rut-message">Formato: 12.345.678-9</div>
                        </td>
                    </tr>
                    <tr>
                        <td class="label">Razón Social</td>
                        <td class="input-cell">
                            <input type="text" name="razon_social" title="Razón Social" required>
                        </td>
                        <td class="label">Rubro</td>
                        <td class="input-cell">
                            <input type="text" name="rubro" title="Rubro" required>
                        </td>
                    </tr>
                    <tr>
                        <td class="label">Contacto</td>
                        <td class="input-cell">
                            <input type="text" name="contacto" title="Contacto" required>
                        </td>
                        <td class="label">Teléfono</td>
                        <td class="input-cell">
                            <input type="tel" name="telefono" title="Teléfono"
                                pattern="[0-9]{9}"
                                maxlength="9"
                                placeholder="912345678"
                                required>
                            <div class="info-message">Debe contener 9 dígitos</div>
                        </td>
                    </tr>
                    <tr>
                        <td class="label">Fecha</td>
                        <td class="input-cell">
                            <input type="date" name="fecha" title="Fecha" value="<?php echo date('Y-m-d'); ?>" required>
                        </td>
                        <td class="label">Estado</td>
                        <td class="input-cell">
                            <select name="estado" title="Estado" required>
                                <option value="">Seleccione...</option>
                                <!-- <option value="Interesado">Interesado</option>
                                <option value="No interesado">No interesado</option> -->
                                <option value="Envio información">Envio información</option>
                                <option value="Negociación">Negociación</option>
                                <option value="Cerrado">Cerrado</option>
                                <option value="B.O. Experian">B.O. Experian</option>
                                <option value="Proceso de Firma">Proceso de Firma</option>
                                <option value="Firmado">Firmado</option>
                                <option value="Habilitado">Habilitado</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td class="label">Observaciones</td>
                        <td class="input-cell">
                            <textarea name="observaciones" title="Observaciones" rows="3" style="width: 100%; resize: vertical;"></textarea>
                        </td>
                        <td class="label"></td>
                        <td class="input-cell"></td>
                    </tr>
                </table>

                <div class="form-actions">
                    <button type="submit" class="btn-submit" style="background-color: green;">
                        <i class="fa fa-save"></i> Guardar prospecto
                    </button>
                    <button type="reset" class="btn-reset">
                        <i class="fa fa-eraser"></i> Limpiar Formulario
                    </button>
                </div>
            </form>
            </div>
        </div>

        <div class="ejecutivos-table-container" style="margin-top: 30px;">
            <div class="section-header">Registros de prospectos</div>

            <!-- Botón para abrir el modal -->
            <div class="button-container" style="margin-bottom: 15px;">
                <button id="openProspectoModal" class="btn-modal-open" style="background-color: green;">
                    <i class="fa fa-plus-circle"></i> Nuevo Prospecto
                </button>
                <button id="exportEjecutivos" class="export-button">
                    <i class="fa fa-download"></i> Descargar Prospectos
                </button>
            </div>
            <div class="table-controls" style="margin-bottom: 15px; display: flex; justify-content: space-between; align-items: center;">
                <input type="text" id="ejecutivos-search" class="table-search" placeholder="Buscar...">
                <?php if (isset($es_admin) && !$es_admin): ?>
                <div class="filter-indicator" style="background-color: #e7f3ff; padding: 5px 10px; border-radius: 4px; font-size: 14px; color: #0056b3;">
                    <i class="fa fa-filter"></i> Mostrando solo sus prospectos
                </div>
                <?php endif; ?>
            </div>
            <div class="table-wrapper">
                <table id="ejecutivos-table">
                    <thead>
                        <tr>
                            <th>Acciones</th>
                            <th>Nombre Ejecutivo</th>
                            <th>RUT</th>
                            <th>Razón Social</th>
                            <th>Rubro</th>
                            <th>Contacto</th>
                            <th>Teléfono</th>
                            <th>Fecha</th>
                            <th>Estado</th>
                            <th>Observaciones</th>
                            <th>Fecha Registro</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- La tabla se llenará dinámicamente con JavaScript -->
                        <tr><td colspan="11" class="loading-data">Cargando datos...</td></tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Bitácora -->
<div id="bitacoraModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <span class="close-modal">&times;</span>
            <h2>Bitácora de Actividades</h2>
            <div id="bitacora-info" class="modal-subtitle"></div>
        </div>
        <div class="modal-body">
            <form id="formBitacora" method="post" action="endpoints/guardar_bitacora.php">
                <input type="hidden" name="rut_ejecutivo" id="bitacora_rut">

                <div class="form-group">
                    <label for="bitacora_estado">Estado:</label>
                    <select name="estado" id="bitacora_estado" required>
                        <option value="">Seleccione...</option>
                        <option value="Envio información">Envio información</option>
                        <option value="Negociación">Negociación</option>
                        <option value="Cerrado">Cerrado</option>
                        <option value="B.O. Experian">B.O. Experian</option>
                        <option value="Proceso de Firma">Proceso de Firma</option>
                        <option value="Firmado">Firmado</option>
                        <option value="Habilitado">Habilitado</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="bitacora_observaciones">Observaciones:</label>
                    <textarea name="observaciones" id="bitacora_observaciones" rows="4" required></textarea>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn-submit">
                        <i class="fa fa-save"></i> Guardar Registro
                    </button>
                </div>
            </form>

            <div class="bitacora-historial">
                <div class="bitacora-header">
                    <h3>Historial de Actividades</h3>
                    <a href="#" class="view-all">Ver todo <i class="fa fa-angle-right"></i></a>
                </div>
                <div class="timeline-container">
                    <div class="timeline" id="bitacora-timeline">
                        <!-- Aquí se cargarán los registros de la bitácora en formato timeline -->
                        <div class="loading-data">Cargando actividades...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Footer Navigation -->
<div class="app-footer">
    <button class="footer-tab active" data-tab="new-tab">
        <i class="fa fa-user-plus"></i>
        <span>Prospecto</span>
    </button>
    <button class="footer-tab" data-tab="form-tab">
        <i class="fa fa-file-text"></i>
        <span>Venta</span>
    </button>
    <?php if ($_SESSION['usuario_id'] == 4): ?>
    <button class="footer-tab" data-tab="table-tab">
        <i class="fa fa-table"></i>
        <span>Registros</span>
    </button>
    <?php endif; ?>
</div>

<!-- Scripts -->
<script>
// Define user permissions for JavaScript (will be overridden by the calculated value below)
window.userIsAdmin = <?php echo $_SESSION['usuario_id'] == 4 ? 'true' : 'false'; ?>;
</script>

<?php
// Pre-load table data to avoid additional requests
// Cargar datos para todos los usuarios

    // Get client records data
    try {
        // Inicializar con un array vacío para evitar errores
        echo "<script>window.preloadedClientData = " . json_encode(['success' => true, 'data' => []]) . ";</script>\n";
    } catch (Exception $e) {
        error_log("Error loading client data: " . $e->getMessage());
        echo "<script>window.preloadedClientData = {success: false, message: 'Error al cargar los datos iniciales: " . addslashes($e->getMessage()) . "'};</script>\n";
    }

    // Get prospects data
    try {
        // Verificar que la función existe antes de usarla
        if (function_exists('createPDOConnection')) {
            $connection = createPDOConnection();
        } else {
            // Alternativa si la función no está disponible
            $connection = new PDO(
                "mysql:host=localhost;dbname=gestarse_experian;charset=utf8",
                'gestarse_ncornejo7_experian',
                'N1c0l7as17',
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
        }
        if ($connection) {
            // Verificar si el usuario tiene rol de administrador
            $usuario_id = $_SESSION['usuario_id'];
            $es_admin = false;
            $query_rol = "SELECT id, correo, nombre_usuario, rol FROM tb_experian_usuarios WHERE id = ?";
            $stmt_rol = $connection->prepare($query_rol);
            $stmt_rol->execute([$usuario_id]);
            $usuario = $stmt_rol->fetch(PDO::FETCH_ASSOC);

            // Registrar información del usuario para depuración
            error_log("form_experian2.php - Usuario ID: " . $usuario_id . " - Datos: " . json_encode($usuario));

            // Forzar a que todos los usuarios que no sean explícitamente administradores vean solo sus prospectos
            if ($usuario && isset($usuario['rol']) && ($usuario['rol'] == 'admin' || $usuario['rol'] == 'administrador')) {
                $es_admin = true;
                error_log("form_experian2.php - Usuario " . $usuario['nombre_usuario'] . " (ID: " . $usuario_id . ") es administrador");
            } else {
                $es_admin = false;
                error_log("form_experian2.php - Usuario " . ($usuario ? $usuario['nombre_usuario'] : 'desconocido') . " (ID: " . $usuario_id . ") NO es administrador");
            }

            // Definir la variable para usar en JavaScript - se hará más adelante en el script principal

            // Fetch prospects with last bitacora data
            if (isset($es_admin) && $es_admin) {
                // Si es admin, mostrar todos los prospectos
                $prospectsQuery = "SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                ORDER BY p.id DESC";
                $prospectsStmt = $connection->prepare($prospectsQuery);
                error_log("form_experian2.php - Consulta SQL para administrador: " . $prospectsQuery);
                $prospectsStmt->execute();
            } else {
                // Si no es admin, filtrar por usuario_id
                $prospectsQuery = "SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC";
                $prospectsStmt = $connection->prepare($prospectsQuery);
                $prospectsStmt->bindParam(':usuario_id', $usuario_id, PDO::PARAM_INT);
                error_log("form_experian2.php - Consulta SQL para usuario regular: " . $prospectsQuery . " (usuario_id = " . $usuario_id . ")");
                $prospectsStmt->execute();
            }

            // Registrar cuántos registros se encontraron
            $prospectsData = $prospectsStmt->fetchAll(PDO::FETCH_ASSOC);
            error_log("form_experian2.php - Se encontraron " . count($prospectsData) . " prospectos para el usuario ID: " . $usuario_id);

            // Convert to JSON for JavaScript
            echo "<script>window.preloadedProspectsData = " . json_encode(['success' => true, 'data' => $prospectsData]) . ";</script>\n";

            // Definir la variable userIsAdmin basada en el rol calculado
            echo "<script>window.userIsAdmin = " . ($es_admin ? "true" : "false") . ";</script>\n";
        } else {
            // Si no hay conexión, definir valores por defecto
            echo "<script>window.preloadedProspectsData = {success: false, message: 'No se pudo establecer conexión con la base de datos'};</script>\n";
            echo "<script>window.userIsAdmin = false;</script>\n";
        }
    } catch (Exception $e) {
        error_log("Error loading prospects data: " . $e->getMessage());
        echo "<script>window.preloadedProspectsData = {success: false, message: 'Error al cargar los datos iniciales: " . addslashes($e->getMessage()) . "'};</script>\n";
        // Definir userIsAdmin como false en caso de error
        echo "<script>window.userIsAdmin = false;</script>\n";
    }
?>

<!-- Pasar estado de conexión a JavaScript -->
<script>
    // Variable global para indicar el estado de la conexión a la base de datos
    window.dbConnectionOk = <?php echo $db_connection_ok ? 'true' : 'false'; ?>;
    <?php if (!$db_connection_ok): ?>
    window.dbConnectionError = "<?php echo addslashes($connection_error_message); ?>";
    <?php endif; ?>
</script>

<script src="<?php echo version_url('js/form_experian.js'); ?>?v=<?php echo filemtime(__DIR__ . '/js/form_experian.js'); ?>"></script>

<!-- Script anti-caché MÍNIMO para evitar bucles -->
<script src="<?php echo version_url('js/no-cache-minimal.js'); ?>"></script>

<script>
// Sistema de localStorage para navegación - Estrategia TQW
(function() {
    // Mantener historial de navegación
    if (typeof localStorage !== "undefined") {
        var currentPage = window.location.href;
        var previousPages = JSON.parse(localStorage.getItem("navigationHistory") || "[]");
        
        // Añadir página actual si no existe
        if (previousPages.indexOf(currentPage) === -1) {
            previousPages.push(currentPage);
            // Mantener solo últimas 5 páginas
            if (previousPages.length > 5) {
                previousPages = previousPages.slice(-5);
            }
            localStorage.setItem("navigationHistory", JSON.stringify(previousPages));
        }
        
        // Registrar estado de sesión para diagnóstico
        localStorage.setItem('ultimaSesionActiva', '<?php echo date('Y-m-d H:i:s'); ?>');
        localStorage.setItem('ultimoUsuarioActivo', '<?php echo $_SESSION['usuario'] ?? ''; ?>');
        localStorage.setItem('ultimaPaginaConSesion', window.location.href);
    }
    
    // Configuración AJAX mejorada
    if (window.jQuery) {
        $.ajaxSetup({
            cache: false // Solo para peticiones AJAX
        });
    }
})();

// DESREGISTRAR SERVICE WORKER para evitar bucles infinitos
function unregisterServiceWorker() {
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.getRegistrations().then(function(registrations) {
            registrations.forEach(function(registration) {
                console.log('🚫 Desregistrando Service Worker:', registration.scope);
                registration.unregister().then(function(success) {
                    if (success) {
                        console.log('✅ Service Worker desregistrado exitosamente');
                    }
                }).catch(function(error) {
                    console.warn('⚠️ Error al desregistrar Service Worker:', error);
                });
            });
        }).catch(function(error) {
            console.warn('⚠️ Error al obtener registros de Service Worker:', error);
        });
    }
}

// Mensaje de bienvenida e inicialización
document.addEventListener('DOMContentLoaded', function() {
    unregisterServiceWorker(); // Desregistrar Service Worker primero
    console.log('Panel de Experian cargado correctamente');

    // Deshabilitar caché para peticiones AJAX
    $.ajaxSetup({
        cache: false
    });
});
</script>


</body>
</html>



