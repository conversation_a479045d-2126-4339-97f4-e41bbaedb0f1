<?php
/**
 * Configuración de Base de Datos Multi-Entorno
 * Compatible con el sistema de detección automática de entorno
 */

// Incluir configuración de entorno si no está cargada
if (!defined('ENVIRONMENT')) {
    require_once dirname(__DIR__) . '/config.php';
}

/**
 * Configuraciones de base de datos por entorno
 */
class DatabaseConfig {
    
    private static $instance = null;
    private $configs = array();
    private $environment = null;
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->environment = defined('ENVIRONMENT') ? ENVIRONMENT : 'development';
        $this->loadConfigurations();
    }
    
    private function loadConfigurations() {
        if ($this->environment === 'development') {
            // Configuraciones para desarrollo local
            $this->configs = array(
                // Opción 1: Conexión remota al hosting (si está permitida)
                'remote_hosting' => array(
                    'host' => 'gestarservicios.cl',
                    'port' => 3306,
                    'database' => 'gestarse_ncornejo7_experian',
                    'username' => 'gestarse_ncornejo7_experian',
                    'password' => 'N1c0l7as17',
                    'timeout' => 5,
                    'priority' => 1
                ),
                // Opción 2: Base de datos local de desarrollo (recomendada)
                'local_dev' => array(
                    'host' => 'localhost',
                    'port' => 3306,
                    'database' => 'gestar_local_dev',
                    'username' => 'root',
                    'password' => '',
                    'timeout' => 2,
                    'priority' => 2
                ),
                // Opción 3: Fallback a configuración mínima
                'local_fallback' => array(
                    'host' => '127.0.0.1',
                    'port' => 3306,
                    'database' => 'test',
                    'username' => 'root',
                    'password' => '',
                    'timeout' => 1,
                    'priority' => 3
                )
            );
        } else {
            // Configuración para producción
            $this->configs = array(
                'production' => array(
                    'host' => 'localhost',
                    'port' => 3306,
                    'database' => 'gestarse_ncornejo7_experian',
                    'username' => 'gestarse_ncornejo7_experian',
                    'password' => 'N1c0l7as17',
                    'timeout' => 10,
                    'priority' => 1
                )
            );
        }
    }
    
    /**
     * Obtiene las configuraciones ordenadas por prioridad
     */
    public function getConfigurations() {
        // Ordenar por prioridad
        uasort($this->configs, function($a, $b) {
            return $a['priority'] - $b['priority'];
        });
        
        return $this->configs;
    }
    
    /**
     * Obtiene una configuración específica
     */
    public function getConfig($name) {
        return isset($this->configs[$name]) ? $this->configs[$name] : null;
    }
    
    /**
     * Obtiene el entorno actual
     */
    public function getEnvironment() {
        return $this->environment;
    }
    
    /**
     * Verifica si una configuración es válida para el entorno actual
     */
    public function isValidForEnvironment($configName) {
        if ($this->environment === 'development') {
            return in_array($configName, array('remote_hosting', 'local_dev', 'local_fallback'));
        } else {
            return $configName === 'production';
        }
    }
}

// Función helper para obtener configuración de BD
if (!function_exists('db_config')) {
    function db_config($name = null) {
        $dbConfig = DatabaseConfig::getInstance();
        return $name === null ? $dbConfig->getConfigurations() : $dbConfig->getConfig($name);
    }
}

// Función helper para verificar entorno de BD
if (!function_exists('db_environment')) {
    function db_environment() {
        return DatabaseConfig::getInstance()->getEnvironment();
    }
}
